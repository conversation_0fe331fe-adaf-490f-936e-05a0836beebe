/**
 * Prompt Registry - MCP Prompts for Centralized Prompt Management
 * Coordinates modular prompt files for reusability, consistency, and versioning
 */
import { businessCasePrompts, businessCasePromptMetadata } from './business-case-prompts.js';
import { conversationPrompts, conversationPromptMetadata } from './conversation-prompts.js';
import { documentPrompts, documentPromptMetadata } from './document-prompts.js';
import { requirementsPrompts, requirementsPromptMetadata } from './requirements-prompts.js';
export class PromptRegistry {
    constructor() {
        this.prompts = new Map();
        this.registerModularPrompts();
    }
    /**
     * Register prompts from modular prompt files
     */
    registerModularPrompts() {
        // Register business case prompts
        Object.entries(businessCasePrompts).forEach(([name, promptFn]) => {
            this.prompts.set(name, promptFn);
        });
        // Register conversation prompts
        Object.entries(conversationPrompts).forEach(([name, promptFn]) => {
            this.prompts.set(name, promptFn);
        });
        // Register document prompts
        Object.entries(documentPrompts).forEach(([name, promptFn]) => {
            this.prompts.set(name, promptFn);
        });
        // Register requirements prompts
        Object.entries(requirementsPrompts).forEach(([name, promptFn]) => {
            this.prompts.set(name, promptFn);
        });
    }
    /**
     * Register a new prompt
     */
    register(name, promptGenerator) {
        this.prompts.set(name, promptGenerator);
    }
    /**
     * Get list of available prompts from all modular prompt files
     */
    listPrompts() {
        return [
            ...businessCasePromptMetadata,
            ...conversationPromptMetadata,
            ...documentPromptMetadata,
            ...requirementsPromptMetadata
        ];
    }
    /**
     * Get prompt by name with arguments
     */
    getPrompt(name, args = {}) {
        const promptGenerator = this.prompts.get(name);
        if (!promptGenerator) {
            throw new Error(`Prompt '${name}' not found`);
        }
        return promptGenerator(args);
    }
}
