/**
 * Mock storage for testing and development
 */
export class MockStorage {
    constructor() {
        this.docs = new Map();
    }
    async save(path, content) {
        this.docs.set(path, { content, savedAt: new Date() });
        return { success: true, url: `mock://${path}` };
    }
    async read(path) {
        const document = this.docs.get(path);
        if (!document) {
            return {
                success: false,
                error: `Document not found: ${path}`
            };
        }
        return {
            success: true,
            content: document.content
        };
    }
    async list() {
        const documents = Array.from(this.docs.entries()).map(([path, doc]) => ({
            path,
            name: path.split('/').pop() || path,
            size: doc.content.length,
            lastModified: doc.savedAt,
            url: `mock://${path}`
        }));
        return {
            success: true,
            documents
        };
    }
    // Legacy methods for backward compatibility
    get(path) {
        const doc = this.docs.get(path);
        return doc?.content;
    }
    getAll() {
        const result = new Map();
        this.docs.forEach((doc, path) => {
            result.set(path, doc.content);
        });
        return result;
    }
}
