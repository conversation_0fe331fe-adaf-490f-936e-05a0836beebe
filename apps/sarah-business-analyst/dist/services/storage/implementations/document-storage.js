/**
 * Document Saver - Single Responsibility
 * Only handles saving documents to storage
 */
export class DocumentStorage {
    constructor(storage) {
        this.storage = storage;
    }
    async save(path, content) {
        try {
            const result = await this.storage.save(path, content);
            return result;
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
}
