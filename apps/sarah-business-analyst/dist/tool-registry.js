/**
 * Tool Registry - Single Responsibility
 * Only handles tool registration and execution
 */
export class ToolRegistry {
    constructor() {
        this.tools = new Map();
    }
    register(tool) {
        const schema = tool.getSchema();
        this.tools.set(schema.name, tool);
    }
    async execute(name, args) {
        const tool = this.tools.get(name);
        if (!tool) {
            throw new Error(`Unknown tool: ${name}`);
        }
        return await tool.execute(args);
    }
    getSchemas() {
        return Array.from(this.tools.values()).map(tool => tool.getSchema());
    }
    has(name) {
        return this.tools.has(name);
    }
}
