/**
 * Simple Project Memory - Single Responsibility
 * Only handles project information storage
 */
/**
 * Clean project memory implementation
 */
export class ProjectMemory {
    constructor() {
        this.info = { information: [] };
    }
    /**
     * Add information to project memory
     */
    add(information) {
        if (!this.info.information.includes(information)) {
            this.info.information.push(information);
        }
    }
    /**
     * Set project name
     */
    setName(name) {
        this.info.name = name;
    }
    /**
     * Get all project information
     */
    getAll() {
        return [...this.info.information];
    }
    /**
     * Get project name
     */
    getName() {
        return this.info.name;
    }
    /**
     * Check if has information
     */
    hasInfo() {
        return this.info.information.length > 0;
    }
    /**
     * Clear all information
     */
    clear() {
        this.info = { information: [] };
    }
    /**
     * Get summary for display
     */
    getSummary() {
        const name = this.info.name || 'Unnamed Project';
        const count = this.info.information.length;
        return `Project: ${name} (${count} pieces of information)`;
    }
}
