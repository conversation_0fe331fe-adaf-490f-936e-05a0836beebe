/**
 * Remember Tool - Single Responsibility
 * Only handles remembering project information
 */
export class RememberTool {
    constructor(projectMemory) {
        this.projectMemory = projectMemory;
    }
    async execute(args) {
        const { information } = args;
        try {
            this.projectMemory.add(information);
            return {
                content: [{
                        type: "text",
                        text: `✅ **Information Remembered**\n\nSaved: "${information}"`
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: "text",
                        text: `❌ Failed to remember: ${error instanceof Error ? error.message : String(error)}`
                    }]
            };
        }
    }
    getSchema() {
        return {
            name: "remember",
            description: "Save important project information",
            inputSchema: {
                type: "object",
                properties: {
                    information: { type: "string", description: "Information to remember" }
                },
                required: ["information"]
            }
        };
    }
}
