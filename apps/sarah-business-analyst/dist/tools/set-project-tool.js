/**
 * Set Project Tool - Single Responsibility
 * Only handles setting project context
 */
export class SetProjectTool {
    constructor(projectMemory) {
        this.projectMemory = projectMemory;
    }
    async execute(args) {
        const { projectName, githubRepo, organization = "Infinisoft-inc" } = args;
        try {
            // Set project name in memory
            this.projectMemory.setName(projectName);
            // Create project context
            const context = {
                name: projectName,
                githubRepo,
                organization,
                docsPath: `projects/${projectName.toLowerCase().replace(/\s+/g, '-')}/docs`
            };
            return {
                content: [{
                        type: "text",
                        text: `✅ **Project Context Set**\n\n**Project:** ${projectName}\n**Repository:** ${organization}/${githubRepo}\n**Docs Path:** ${context.docsPath}`
                    }],
                context
            };
        }
        catch (error) {
            return {
                content: [{
                        type: "text",
                        text: `❌ Failed to set project: ${error instanceof Error ? error.message : String(error)}`
                    }]
            };
        }
    }
    getSchema() {
        return {
            name: "set-project",
            description: "Set project context for document generation",
            inputSchema: {
                type: "object",
                properties: {
                    projectName: { type: "string", description: "Name of the project" },
                    githubRepo: { type: "string", description: "GitHub repository name" },
                    organization: { type: "string", description: "GitHub organization", default: "Infinisoft-inc" }
                },
                required: ["projectName", "githubRepo"]
            }
        };
    }
}
