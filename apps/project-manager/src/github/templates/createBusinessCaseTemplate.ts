/**
 * Business Case Template for AI-SDLC Methodology
 * Single Responsibility: Create business case document template
 */

export function createBusinessCaseTemplate(): string {
  return `# Business Case

**Project:** [Project Name]
**Date:** [Date]
**Prepared by:** Sarah - AI Business Analyst
**Status:** [Draft/Review/Approved]

## 📋 Executive Summary

[Brief overview of the business problem and proposed solution]

## 🎯 Problem Statement

### Current Situation
[Describe the current state and challenges]

### Business Impact
[Quantify the impact of not addressing this problem]

## 💡 Proposed Solution

### Solution Overview
[High-level description of the proposed solution]

### Key Benefits
- [Benefit 1]
- [Benefit 2]
- [Benefit 3]

## 📊 Business Justification

### Return on Investment (ROI)
[Expected ROI and timeframe]

### Cost-Benefit Analysis
| Category | Cost | Benefit |
|----------|------|---------|
| Development | [Amount] | [Value] |
| Operations | [Amount] | [Value] |
| **Total** | **[Total Cost]** | **[Total Benefit]** |

## 🎯 Success Criteria

1. [Measurable success criterion 1]
2. [Measurable success criterion 2]
3. [Measurable success criterion 3]

## ⏱️ Timeline

| Phase | Duration | Key Milestones |
|-------|----------|----------------|
| Planning | [Duration] | [Milestones] |
| Development | [Duration] | [Milestones] |
| Deployment | [Duration] | [Milestones] |

## 🚨 Risks and Mitigation

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| [Risk 1] | [High/Medium/Low] | [High/Medium/Low] | [Strategy] |
| [Risk 2] | [High/Medium/Low] | [High/Medium/Low] | [Strategy] |

## 👥 Stakeholders

| Stakeholder | Role | Involvement Level |
|-------------|------|------------------|
| [Name/Role] | [Description] | [High/Medium/Low] |

## 📝 Recommendations

[Clear recommendation and next steps]

---

**Next Phase:** Business Requirements Document (BRD)
**AI Teammate:** Sarah - AI Business Analyst
**Human Approval Required:** ✅`;
}
