{"scripts": {"preinstall": "node ./scripts/enforce-pnpm.js", "build-all": "pnpm -r run build", "clean": "rimraf 'packages/*/{dist,node_modules}' && rimraf 'apps/*/{dist,node_modules}' && rimraf node_modules", "watch-all": "pnpm -r run watch", "new-app": "bash scripts/new-app.sh", "add-package": "bash scripts/add-package.sh"}, "keywords": [], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "packageManager": "pnpm@10.11.1", "devDependencies": {"rimraf": "^6.0.1", "typescript": "^5.8.3"}}