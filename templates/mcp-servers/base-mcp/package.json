{"name": "ai-teammate-template", "version": "2.0.1", "private": true, "description": "AI-SDLC Teammate Template v2.0 - Intelligent AI teammates with persistent memory and voice-first interaction", "type": "module", "main": "dist/index.js", "bin": {"ai-teammate-template": "dist/index.js"}, "scripts": {"build": "tsc && chmod +x dist/index.js", "dev": "tsc --watch", "start": "node dist/index.js", "prepare": "npm run build"}, "keywords": ["ai-sdlc", "ai-teammate", "mcp-server", "persistent-memory", "voice-first", "intelligent-ai", "template"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@dopplerhq/node-sdk": "1.3.0", "@modelcontextprotocol/sdk": "^0.5.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}