{"testScenario": {"name": "E-Commerce Platform", "description": "Comprehensive test of GitHub Service with realistic project structure", "complexity": "High - 2 Epics, 4 Features, 9 Tasks", "estimatedTime": "45-60 seconds for complete creation", "projectConfig": {"repository": {"name": "ecommerce-platform-test", "description": "Test repository for e-commerce platform development with full GitHub Service integration", "private": false}, "epic": {"title": "[EPIC] User Management System", "body": "# User Management System Epic\n\nComplete user management functionality including registration, authentication, and profile management.\n\n## Scope\n- User registration and login\n- Authentication and authorization\n- User profile management\n- Security and data protection\n\n## Acceptance Criteria\n- [ ] Users can register with email validation\n- [ ] Secure authentication system\n- [ ] Profile management capabilities\n- [ ] Password security compliance", "labels": ["epic", "user-management", "authentication"]}, "features": [{"title": "[FEATURE] User Registration & Authentication", "body": "# User Registration & Authentication\n\nImplement secure user registration and authentication system.\n\n## Requirements\n- Email validation system\n- Password encryption and security\n- JWT token management\n- Session handling\n\n## Technical Specifications\n- Use bcrypt for password hashing\n- JWT tokens for session management\n- Email verification workflow\n- Rate limiting for login attempts", "labels": ["feature", "authentication", "security"], "tasks": [{"title": "[TASK] Email validation system", "body": "# Email Validation System\n\nImplement comprehensive email validation for user registration.\n\n## Requirements\n- Email format validation\n- Domain verification\n- Disposable email detection\n- Email verification workflow\n\n## Implementation\n- Use regex for format validation\n- DNS lookup for domain verification\n- Send verification emails\n- Handle verification responses", "labels": ["task", "email", "validation"]}, {"title": "[TASK] Password encryption", "body": "# Password Encryption\n\nImplement secure password encryption and validation.\n\n## Requirements\n- bcrypt hashing algorithm\n- Salt generation\n- Password strength validation\n- Secure storage\n\n## Security Standards\n- Minimum 12 rounds for bcrypt\n- Password complexity requirements\n- No plain text storage\n- Secure comparison methods", "labels": ["task", "password", "encryption", "security"]}, {"title": "[TASK] JWT token management", "body": "# JWT Token Management\n\nImplement JWT token creation, validation, and refresh mechanisms.\n\n## Requirements\n- Token generation on login\n- Token validation middleware\n- Refresh token mechanism\n- Token expiration handling\n\n## Implementation\n- Use RS256 algorithm\n- Short-lived access tokens\n- Long-lived refresh tokens\n- Secure token storage", "labels": ["task", "jwt", "tokens", "authentication"]}]}, {"title": "[FEATURE] User Profile Management", "body": "# User Profile Management\n\nComplete user profile management system with CRUD operations.\n\n## Requirements\n- Profile creation and editing\n- Avatar upload system\n- Privacy settings\n- Profile validation\n\n## Features\n- Personal information management\n- Profile picture upload\n- Account settings\n- Privacy controls", "labels": ["feature", "profile", "user-management"], "tasks": [{"title": "[TASK] Profile CRUD operations", "body": "# Profile CRUD Operations\n\nImplement Create, Read, Update, Delete operations for user profiles.\n\n## Requirements\n- Create new profiles\n- Read profile information\n- Update profile data\n- Delete profiles (soft delete)\n\n## API Endpoints\n- POST /api/profiles\n- GET /api/profiles/:id\n- PUT /api/profiles/:id\n- DELETE /api/profiles/:id", "labels": ["task", "crud", "api", "profiles"]}, {"title": "[TASK] Avatar upload system", "body": "# Avatar Upload System\n\nImplement secure avatar upload and management system.\n\n## Requirements\n- Image upload validation\n- Image resizing and optimization\n- Secure file storage\n- Avatar deletion\n\n## Technical Specifications\n- Support JPEG, PNG formats\n- Maximum file size: 5MB\n- Resize to standard dimensions\n- CDN integration for delivery", "labels": ["task", "upload", "images", "storage"]}]}]}, "additionalEpic": {"title": "[EPIC] Product Catalog System", "body": "# Product Catalog System Epic\n\nComplete product management and catalog functionality for e-commerce platform.\n\n## Scope\n- Product management (CRUD)\n- Product search and filtering\n- Category management\n- Inventory tracking\n\n## Acceptance Criteria\n- [ ] Product creation and management\n- [ ] Advanced search capabilities\n- [ ] Category organization\n- [ ] Real-time inventory updates", "labels": ["epic", "products", "catalog"], "features": [{"title": "[FEATURE] Product Management", "body": "# Product Management\n\nComplete product management system with full CRUD operations.\n\n## Requirements\n- Product creation and editing\n- Image upload system\n- Category assignment\n- Inventory management\n\n## Features\n- Product information management\n- Multiple image support\n- Category relationships\n- Stock level tracking", "labels": ["feature", "products", "management"], "tasks": [{"title": "[TASK] Product CRUD API", "body": "# Product CRUD API\n\nImplement comprehensive API for product management operations.\n\n## Requirements\n- Create new products\n- Read product information\n- Update product details\n- Delete products (soft delete)\n\n## API Endpoints\n- POST /api/products\n- GET /api/products/:id\n- PUT /api/products/:id\n- DELETE /api/products/:id\n- GET /api/products (with pagination)", "labels": ["task", "api", "products", "crud"]}, {"title": "[TASK] Image upload system", "body": "# Product Image Upload System\n\nImplement multi-image upload system for products.\n\n## Requirements\n- Multiple image upload\n- Image validation and processing\n- Image ordering and management\n- Thumbnail generation\n\n## Technical Specifications\n- Support multiple formats\n- Automatic thumbnail creation\n- Image compression\n- CDN integration", "labels": ["task", "images", "upload", "products"]}]}, {"title": "[FEATURE] Search & Filtering", "body": "# Search & Filtering\n\nAdvanced product search and filtering capabilities.\n\n## Requirements\n- Text search functionality\n- Category filtering\n- Price range filtering\n- Advanced filter combinations\n\n## Features\n- Full-text search\n- Faceted search\n- Search suggestions\n- Filter persistence", "labels": ["feature", "search", "filtering"], "tasks": [{"title": "[TASK] Search algorithm", "body": "# Search Algorithm Implementation\n\nImplement efficient product search algorithm with relevance scoring.\n\n## Requirements\n- Full-text search capability\n- Relevance scoring\n- Search result ranking\n- Performance optimization\n\n## Implementation\n- Elasticsearch integration\n- Search indexing\n- Query optimization\n- Result caching", "labels": ["task", "search", "algorithm", "elasticsearch"]}, {"title": "[TASK] Filter UI components", "body": "# Filter UI Components\n\nCreate interactive filtering interface components.\n\n## Requirements\n- Category filter checkboxes\n- Price range sliders\n- Brand selection\n- Filter combination logic\n\n## UI Components\n- Filter sidebar\n- Active filter display\n- Clear filters functionality\n- Mobile-responsive design", "labels": ["task", "ui", "filters", "components"]}]}]}, "expectedResults": {"repository": {"name": "ecommerce-platform-test", "issueCount": 12, "branchCount": 13}, "issues": {"epics": 2, "features": 4, "tasks": 9, "totalIssues": 15}, "branches": {"epicBranches": 2, "featureBranches": 4, "taskBranches": 9, "totalBranches": 15}, "relationships": {"epicToFeature": 4, "featureToTask": 9}}, "validationChecklist": ["Repository created successfully", "All 15 issues created with proper titles", "All 15 branches created and linked", "2 Epic issues have Epic type assigned", "4 Feature issues have Feature type assigned", "9 Task issues have Task type assigned", "All parent-child relationships established", "GitHub Project created and configured", "All issues added to project", "Project hierarchy visible in GitHub UI", "Branch naming follows convention", "All issues have proper labels", "No API errors during creation", "Total creation time under 60 seconds"]}}