{"name": "@brainstack/github-service", "version": "0.1.0", "description": "Simple GitHub App service for AI-SDLC project management", "type": "module", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc", "test": "NODE_OPTIONS='--experimental-vm-modules' jest", "test:watch": "NODE_OPTIONS='--experimental-vm-modules' jest --watch", "test:coverage": "NODE_OPTIONS='--experimental-vm-modules' jest --coverage", "test:atomic": "NODE_OPTIONS='--experimental-vm-modules' jest tests/atomic", "test:compositions": "NODE_OPTIONS='--experimental-vm-modules' jest tests/compositions", "test:integration": "NODE_OPTIONS='--experimental-vm-modules' jest tests/integration", "example:basic": "node --experimental-vm-modules examples/basic-usage.ts", "example:advanced": "node --experimental-vm-modules examples/advanced-composition.ts"}, "dependencies": {"@brainstack/integration-service": "workspace:*", "@brainstack/log": "1.1.163", "@dopplerhq/node-sdk": "^1.3.0", "@octokit/rest": "^20.0.0", "@octokit/webhooks": "14.0.2", "@octokit/webhooks-types": "7.6.1", "@supabase/supabase-js": "^2.50.0", "jsonwebtoken": "^9.0.0"}, "devDependencies": {"@types/jest": "29.5.14", "@types/jsonwebtoken": "^9.0.0", "@types/node": "^20.0.0", "dotenv": "^16.3.1", "jest": "29.7.0", "ts-jest": "29.3.4", "typescript": "^5.0.0"}, "keywords": ["github", "api", "webhook", "ai-sdlc"], "author": "<PERSON> <<EMAIL>>", "license": "MIT"}