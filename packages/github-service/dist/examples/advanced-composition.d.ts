/**
 * Advanced Composition Example
 * Demonstrates creating custom compositions using atomic functions
 */
import type { GitHubIssueData, Result } from '../src/github/types';
/**
 * Custom composition: Create Bug Report with full workflow
 */
declare function createBugReport(octokit: any, owner: string, repo: string, bugData: GitHubIssueData & {
    severity: 'low' | 'medium' | 'high' | 'critical';
    reproductionSteps: string[];
    expectedBehavior: string;
    actualBehavior: string;
}): Promise<Result<any>>;
/**
 * Custom composition: Create Release Planning Epic
 */
declare function createReleasePlan(octokit: any, owner: string, repo: string, releaseData: {
    version: string;
    targetDate: string;
    features: string[];
    bugFixes: string[];
}): Promise<Result<any>>;
declare function advancedCompositionExample(): Promise<void>;
export { advancedCompositionExample, createBugReport, createReleasePlan };
//# sourceMappingURL=advanced-composition.d.ts.map