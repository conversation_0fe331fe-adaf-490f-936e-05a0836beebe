/**
 * GitHub Service - Clean Architecture Implementation
 *
 * Pure SRP (Single Responsibility Principle) architecture with:
 * - Layer 1: Atomic GitHub functions (1 responsibility per function)
 * - Layer 2: Composition functions (preserve AI-SDLC customizations)
 * - Dependency injection throughout
 * - Comprehensive testing with real API integration
 */
export * from './github/index.js';
export * from './compositions/index.js';
export * from './types.js';
//# sourceMappingURL=index.d.ts.map