/**
 * Create Feature composition
 * Composes: createIssue + setIssueType + addSubIssue + createLinkedBranch
 * Preserves all Feature customizations
 */
import type { Octokit } from '@octokit/rest';
import type { GitHubIssueData, Result } from '../github/types';
export interface FeatureData extends GitHubIssueData {
    parentEpicNumber?: number;
    frReference?: string;
}
export interface FeatureResponse {
    id: number;
    number: number;
    node_id: string;
    title: string;
    html_url: string;
    state: string;
    parentEpicNumber?: number;
    linkedBranch?: {
        branchName: string;
        branchId: string;
        commitOid: string;
        issueNumber: number;
    };
}
export declare function createFeature(octokit: Octokit, owner: string, repo: string, featureData: FeatureData): Promise<Result<FeatureResponse>>;
//# sourceMappingURL=createFeature.d.ts.map