/**
 * GitHub Service - Composition Functions
 * Export all composition functions that preserve customizations
 */
export { createEpic } from './createEpic.js';
export type { EpicData, EpicResponse } from './createEpic.js';
export { createFeature } from './createFeature.js';
export type { FeatureData, FeatureResponse } from './createFeature.js';
export { createTask } from './createTask.js';
export type { TaskData, TaskResponse } from './createTask.js';
//# sourceMappingURL=index.d.ts.map