/**
 * Create Task composition
 * Composes: createIssue + setIssueType + addSubIssue + createLinkedBranch
 * Preserves all Task customizations
 */
import type { Octokit } from '@octokit/rest';
import type { GitHubIssueData, Result } from '../github/types';
export interface TaskData extends GitHubIssueData {
    parentFeatureNumber?: number;
}
export interface TaskResponse {
    id: number;
    number: number;
    node_id: string;
    title: string;
    html_url: string;
    state: string;
    parentFeatureNumber?: number;
    linkedBranch?: {
        branchName: string;
        branchId: string;
        commitOid: string;
        issueNumber: number;
    };
}
export declare function createTask(octokit: Octokit, owner: string, repo: string, taskData: TaskData): Promise<Result<TaskResponse>>;
//# sourceMappingURL=createTask.d.ts.map