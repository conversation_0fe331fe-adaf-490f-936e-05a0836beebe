/**
 * Create Epic composition
 * Composes: createIssue + setIssueType + createLinkedBranch
 * Preserves all Epic customizations
 */
import type { Octokit } from '@octokit/rest';
import type { GitHubIssueData, Result } from '../github/types';
export interface EpicData extends GitHubIssueData {
}
export interface EpicResponse {
    id: number;
    number: number;
    node_id: string;
    title: string;
    html_url: string;
    state: string;
    linkedBranch?: {
        branchName: string;
        branchId: string;
        commitOid: string;
        issueNumber: number;
    };
}
export declare function createEpic(octokit: Octokit, owner: string, repo: string, epicData: EpicData): Promise<Result<EpicResponse>>;
//# sourceMappingURL=createEpic.d.ts.map