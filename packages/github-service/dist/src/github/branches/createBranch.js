/**
 * Create GitHub branch
 * Single responsibility: Create a new branch from a commit SHA
 */
export async function createBranch(octokit, owner, repo, branchData) {
    try {
        const response = await octokit.rest.git.createRef({
            owner,
            repo,
            ref: `refs/heads/${branchData.name}`,
            sha: branchData.sha,
        });
        return {
            success: true,
            data: {
                name: branchData.name,
                sha: response.data.object.sha,
                url: response.data.url,
            }
        };
    }
    catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}
