/**
 * Create linked branch for GitHub issue
 * Single responsibility: Create a branch linked to an issue using GraphQL
 */
import type { Octokit } from '@octokit/rest';
import type { LinkedBranchData, Result } from '../types';
export declare function createLinkedBranch(octokit: Octokit, owner: string, repo: string, issueNumber: number, branchName: string): Promise<Result<LinkedBranchData>>;
//# sourceMappingURL=createLinkedBranch.d.ts.map