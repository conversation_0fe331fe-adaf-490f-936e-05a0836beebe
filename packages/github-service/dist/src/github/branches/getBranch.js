/**
 * Get GitHub branch information
 * Single responsibility: Retrieve branch details
 */
export async function getBranch(octokit, owner, repo, branchName) {
    try {
        const response = await octokit.rest.repos.getBranch({
            owner,
            repo,
            branch: branchName,
        });
        return {
            success: true,
            data: {
                name: response.data.name,
                sha: response.data.commit.sha,
                url: response.data._links.html,
            }
        };
    }
    catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}
