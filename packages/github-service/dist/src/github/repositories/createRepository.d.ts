/**
 * Create GitHub repository
 * Single responsibility: Create a new repository
 */
import type { Octokit } from '@octokit/rest';
import type { GitHubRepositoryData, GitHubRepositoryResponse, Result } from '../types';
export declare function createRepository(octokit: Octokit, repositoryData: GitHubRepositoryData): Promise<Result<GitHubRepositoryResponse>>;
//# sourceMappingURL=createRepository.d.ts.map