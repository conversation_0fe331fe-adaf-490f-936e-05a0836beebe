/**
 * List accessible repositories
 * Single responsibility: List repositories accessible to the installation
 */
export async function listRepositories(octokit) {
    try {
        const response = await octokit.rest.apps.listReposAccessibleToInstallation({
            per_page: 100,
        });
        const repos = response.data.repositories.map((repo) => ({
            id: repo.id,
            name: repo.name,
            full_name: repo.full_name,
            html_url: repo.html_url,
            clone_url: repo.clone_url,
            ssh_url: repo.ssh_url,
            private: repo.private,
            default_branch: repo.default_branch,
        }));
        return {
            success: true,
            data: repos
        };
    }
    catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}
