/**
 * Create GitHub repository in organization
 * Single responsibility: Create a new repository in an organization
 */
import type { Octokit } from '@octokit/rest';
import type { GitHubRepositoryData, GitHubRepositoryResponse, Result } from '../types';
export declare function createOrgRepository(octokit: Octokit, org: string, repositoryData: GitHubRepositoryData): Promise<Result<GitHubRepositoryResponse>>;
//# sourceMappingURL=createOrgRepository.d.ts.map