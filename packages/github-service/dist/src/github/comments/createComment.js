/**
 * Create GitHub issue comment
 * Single responsibility: Add a comment to an issue
 */
export async function createComment(octokit, owner, repo, issueNumber, commentData) {
    try {
        const response = await octokit.rest.issues.createComment({
            owner,
            repo,
            issue_number: issueNumber,
            body: commentData.body,
        });
        return {
            success: true,
            data: {
                id: response.data.id,
                html_url: response.data.html_url,
                body: response.data.body || '',
                created_at: response.data.created_at,
            }
        };
    }
    catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}
