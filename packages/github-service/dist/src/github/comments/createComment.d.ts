/**
 * Create GitHub issue comment
 * Single responsibility: Add a comment to an issue
 */
import type { Octokit } from '@octokit/rest';
import type { CommentData, CommentResponse, Result } from '../types';
export declare function createComment(octokit: Octokit, owner: string, repo: string, issueNumber: number, commentData: CommentData): Promise<Result<CommentResponse>>;
//# sourceMappingURL=createComment.d.ts.map