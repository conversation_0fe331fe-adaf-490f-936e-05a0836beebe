/**
 * Add label to GitHub issue
 * Single responsibility: Add a single label to an issue
 */
export async function addIssueLabel(octokit, owner, repo, issueNumber, label) {
    try {
        await octokit.rest.issues.addLabels({
            owner,
            repo,
            issue_number: issueNumber,
            labels: [label],
        });
        return { success: true };
    }
    catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}
