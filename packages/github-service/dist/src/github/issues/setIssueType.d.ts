/**
 * Set GitHub issue type using GraphQL
 * Single responsibility: Set issue type via GitHub's GraphQL API
 */
import type { Octokit } from '@octokit/rest';
import type { Result } from '../types';
export declare function setIssueType(octokit: Octokit, issueNodeId: string, issueTypeId: string): Promise<Result<void>>;
/**
 * Set issue type by name (ensures issue types exist and uses real IDs)
 */
export declare function setIssueTypeByName(octokit: Octokit, owner: string, repo: string, issueNumber: number, typeName: string): Promise<Result<void>>;
//# sourceMappingURL=setIssueType.d.ts.map