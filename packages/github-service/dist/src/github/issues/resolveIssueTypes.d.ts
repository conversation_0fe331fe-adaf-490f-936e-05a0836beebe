/**
 * Resolve real issue types from repository using labels
 * Single responsibility: Get actual issue type labels from GitHub repository
 */
import type { Octokit } from '@octokit/rest';
import type { Result } from '../types';
export interface IssueTypeMap {
    [typeName: string]: string;
}
export interface ResolvedIssueTypes {
    Epic?: string;
    Feature?: string;
    Task?: string;
    Bug?: string;
    Enhancement?: string;
}
/**
 * Get real issue types from repository labels
 */
export declare function resolveIssueTypes(octokit: Octokit, owner: string, repo: string): Promise<Result<ResolvedIssueTypes>>;
/**
 * Check if issue types are available for repository
 */
export declare function hasIssueTypes(octokit: Octokit, owner: string, repo: string): Promise<boolean>;
/**
 * Get specific issue type ID
 */
export declare function getIssueTypeId(octokit: Octokit, owner: string, repo: string, typeName: keyof ResolvedIssueTypes): Promise<string | undefined>;
/**
 * Clear issue type cache (useful for testing)
 */
export declare function clearIssueTypeCache(): void;
//# sourceMappingURL=resolveIssueTypes.d.ts.map