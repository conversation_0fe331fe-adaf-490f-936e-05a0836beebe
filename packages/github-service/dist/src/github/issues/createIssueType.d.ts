/**
 * Create GitHub issue type for organization
 * Single responsibility: Create a new issue type at the organization level
 */
import type { Octokit } from '@octokit/rest';
import type { Result } from '../types';
export interface IssueTypeData {
    name: string;
    description?: string;
    color?: 'gray' | 'blue' | 'green' | 'yellow' | 'orange' | 'red' | 'pink' | 'purple' | null;
    is_enabled?: boolean;
}
export interface IssueTypeResponse {
    id: number;
    node_id: string;
    name: string;
    description: string | null;
    color: string | null;
    is_enabled: boolean;
    created_at?: string;
    updated_at?: string;
}
/**
 * Create issue type for organization
 */
export declare function createIssueType(octokit: Octokit, org: string, issueTypeData: IssueTypeData): Promise<Result<IssueTypeResponse>>;
/**
 * Get all issue types for organization
 */
export declare function getOrganizationIssueTypes(octokit: Octokit, org: string): Promise<Result<IssueTypeResponse[]>>;
/**
 * Ensure issue types exist for organization
 */
export declare function ensureIssueTypes(octokit: Octokit, org: string): Promise<Result<Record<string, string>>>;
//# sourceMappingURL=createIssueType.d.ts.map