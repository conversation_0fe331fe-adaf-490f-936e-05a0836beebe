/**
 * Resolve real issue types from repository using labels
 * Single responsibility: Get actual issue type labels from GitHub repository
 */
/**
 * Cache for issue types to avoid repeated API calls
 */
const issueTypeCache = new Map();
/**
 * Get real issue types from repository labels
 */
export async function resolveIssueTypes(octokit, owner, repo) {
    try {
        const cacheKey = `${owner}/${repo}`;
        // Return cached result if available
        if (issueTypeCache.has(cacheKey)) {
            return {
                success: true,
                data: issueTypeCache.get(cacheKey)
            };
        }
        // Get repository labels to find issue type labels
        const labelsResponse = await octokit.rest.issues.listLabelsForRepo({
            owner,
            repo,
            per_page: 100
        });
        const resolvedTypes = {};
        // Map labels to issue types
        labelsResponse.data.forEach(label => {
            const labelName = label.name.toLowerCase();
            if (labelName === 'epic') {
                resolvedTypes.Epic = label.name;
            }
            else if (labelName === 'feature') {
                resolvedTypes.Feature = label.name;
            }
            else if (labelName === 'task') {
                resolvedTypes.Task = label.name;
            }
            else if (labelName === 'bug') {
                resolvedTypes.Bug = label.name;
            }
            else if (labelName === 'enhancement') {
                resolvedTypes.Enhancement = label.name;
            }
        });
        // Cache the result
        issueTypeCache.set(cacheKey, resolvedTypes);
        console.log(`✅ Found issue type labels: ${Object.keys(resolvedTypes).join(', ')}`);
        return {
            success: true,
            data: resolvedTypes
        };
    }
    catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}
/**
 * Check if issue types are available for repository
 */
export async function hasIssueTypes(octokit, owner, repo) {
    const result = await resolveIssueTypes(octokit, owner, repo);
    if (!result.success)
        return false;
    return Object.keys(result.data).length > 0;
}
/**
 * Get specific issue type ID
 */
export async function getIssueTypeId(octokit, owner, repo, typeName) {
    const result = await resolveIssueTypes(octokit, owner, repo);
    if (!result.success)
        return undefined;
    return result.data[typeName];
}
/**
 * Clear issue type cache (useful for testing)
 */
export function clearIssueTypeCache() {
    issueTypeCache.clear();
}
