/**
 * Get available GitHub issue types for a repository
 * Single responsibility: Retrieve issue types via GitHub's GraphQL API
 */
import type { Octokit } from '@octokit/rest';
import type { Result } from '../types';
export interface IssueType {
    id: string;
    name: string;
    description?: string;
}
export declare function getIssueTypes(octokit: Octokit, owner: string, repo: string): Promise<Result<Record<string, string>>>;
//# sourceMappingURL=getIssueTypes.d.ts.map