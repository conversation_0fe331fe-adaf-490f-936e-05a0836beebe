/**
 * Create GitHub issue type for organization
 * Single responsibility: Create a new issue type at the organization level
 */
/**
 * Create issue type for organization
 */
export async function createIssueType(octokit, org, issueTypeData) {
    try {
        const response = await octokit.request('POST /orgs/{org}/issue-types', {
            org,
            name: issueTypeData.name,
            description: issueTypeData.description || null,
            color: issueTypeData.color || null,
            is_enabled: issueTypeData.is_enabled ?? true,
            headers: {
                'Accept': 'application/vnd.github+json',
                'X-GitHub-Api-Version': '2022-11-28'
            }
        });
        return {
            success: true,
            data: response.data
        };
    }
    catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}
/**
 * Get all issue types for organization
 */
export async function getOrganizationIssueTypes(octokit, org) {
    try {
        const response = await octokit.request('GET /orgs/{org}/issue-types', {
            org,
            headers: {
                'Accept': 'application/vnd.github+json',
                'X-GitHub-Api-Version': '2022-11-28'
            }
        });
        return {
            success: true,
            data: response.data
        };
    }
    catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}
/**
 * Ensure issue types exist for organization
 */
export async function ensureIssueTypes(octokit, org) {
    try {
        // First, try to get existing issue types
        const existingResult = await getOrganizationIssueTypes(octokit, org);
        const issueTypeMap = {};
        if (existingResult.success) {
            // Map existing issue types
            existingResult.data.forEach(type => {
                issueTypeMap[type.name] = type.node_id;
            });
        }
        // Define required issue types
        const requiredTypes = [
            {
                name: 'Epic',
                description: 'Large feature or initiative spanning multiple features',
                color: 'purple',
                is_enabled: true
            },
            {
                name: 'Feature',
                description: 'New feature or enhancement',
                color: 'green',
                is_enabled: true
            },
            {
                name: 'Task',
                description: 'Specific task or work item',
                color: 'blue',
                is_enabled: true
            },
            {
                name: 'Bug',
                description: 'Bug or issue to fix',
                color: 'red',
                is_enabled: true
            },
            {
                name: 'Enhancement',
                description: 'Improvement to existing functionality',
                color: 'yellow',
                is_enabled: true
            }
        ];
        // Create missing issue types
        for (const typeData of requiredTypes) {
            if (!issueTypeMap[typeData.name]) {
                const createResult = await createIssueType(octokit, org, typeData);
                if (createResult.success) {
                    issueTypeMap[typeData.name] = createResult.data.node_id;
                    console.log(`✅ Created issue type: ${typeData.name}`);
                }
                else {
                    console.log(`⚠️ Could not create issue type ${typeData.name}: ${createResult.error}`);
                }
            }
        }
        return {
            success: true,
            data: issueTypeMap
        };
    }
    catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}
