/**
 * Get available GitHub issue types for a repository
 * Single responsibility: Retrieve issue types via GitHub's GraphQL API
 */
export async function getIssueTypes(octokit, owner, repo) {
    try {
        const query = `
      query GetRepositoryIssueTypes($owner: String!, $repo: String!) {
        repository(owner: $owner, name: $repo) {
          issueTypes(first: 10) {
            nodes {
              id
              name
              description
            }
          }
        }
      }
    `;
        const response = await octokit.graphql({
            query,
            owner,
            repo,
            headers: {
                'GraphQL-Features': 'issue_types'
            }
        });
        const issueTypes = response.repository?.issueTypes?.nodes || [];
        // Convert to a map of name -> id for easy lookup
        const issueTypeMap = {};
        issueTypes.forEach((type) => {
            issueTypeMap[type.name] = type.id;
        });
        return {
            success: true,
            data: issueTypeMap
        };
    }
    catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}
