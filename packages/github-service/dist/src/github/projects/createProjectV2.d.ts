/**
 * Create GitHub Project v2
 * Single responsibility: Create a new GitHub Project v2 via GraphQL API
 */
import type { Octokit } from '@octokit/rest';
import type { Result } from '../types';
export interface ProjectData {
    title: string;
    body?: string;
    public?: boolean;
}
export interface ProjectResult {
    id: string;
    number: number;
    title: string;
    url: string;
}
export declare function createProjectV2(octokit: Octokit, owner: string, projectData: ProjectData): Promise<Result<ProjectResult>>;
//# sourceMappingURL=createProjectV2.d.ts.map