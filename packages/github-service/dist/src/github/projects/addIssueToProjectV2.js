/**
 * Add issue to GitHub Project v2
 * Single responsibility: Add an issue to a GitHub Project v2 via GraphQL API
 */
export async function addIssueToProjectV2(octokit, projectId, issueNodeId) {
    try {
        const mutation = `
      mutation AddProjectV2ItemById($projectId: ID!, $contentId: ID!) {
        addProjectV2ItemById(input: {
          projectId: $projectId
          contentId: $contentId
        }) {
          item {
            id
            project {
              id
            }
            content {
              ... on Issue {
                id
                number
              }
            }
          }
        }
      }
    `;
        const response = await octokit.graphql({
            query: mutation,
            projectId,
            contentId: issueNodeId
        });
        const item = response.addProjectV2ItemById.item;
        return {
            success: true,
            data: {
                id: item.id,
                projectId: item.project.id,
                issueId: item.content.id
            }
        };
    }
    catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}
