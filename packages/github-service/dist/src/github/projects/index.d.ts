/**
 * GitHub Projects V2 functionality
 * Enhanced with AI Teammate assignment capabilities
 */
export { createProjectField, createAITeammateField, getProjectFields, findAITeammateField, type ProjectFieldData, type ProjectFieldOption, type ProjectFieldResponse } from './createProjectField.js';
export { assignAITeammate, getAIAssignments, getAllAIAssignments, type AITeammate, type ProjectItemAssignment, type AssignmentResult } from './assignAITeammate.js';
//# sourceMappingURL=index.d.ts.map