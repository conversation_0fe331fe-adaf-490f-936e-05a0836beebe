/**
 * AI Teammate assignment functions
 * Single responsibility: Assign and query AI teammates on project items
 */
import type { Octokit } from '@octokit/rest';
import type { Result } from '../types';
export type AITeammate = 'Sarah - AI Business Analyst' | 'Alex - AI Architect' | 'Jordan - AI Project Manager' | 'Unassigned';
export interface ProjectItemAssignment {
    itemId: string;
    issueNumber: number;
    title: string;
    url: string;
    aiTeammate?: AITeammate;
}
export interface AssignmentResult {
    itemId: string;
    fieldId: string;
    optionId: string;
    aiTeammate: AITeammate;
}
/**
 * Assign AI teammate to a project item
 */
export declare function assignAITeammate(octokit: Octokit, projectId: string, itemId: string, aiTeammate: AITeammate): Promise<Result<AssignmentResult>>;
/**
 * Get all project items assigned to a specific AI teammate
 */
export declare function getAIAssignments(octokit: Octokit, projectId: string, aiTeammate: AITeammate): Promise<Result<ProjectItemAssignment[]>>;
/**
 * Get all project items with their AI teammate assignments
 */
export declare function getAllAIAssignments(octokit: Octokit, projectId: string): Promise<Result<ProjectItemAssignment[]>>;
//# sourceMappingURL=assignAITeammate.d.ts.map