/**
 * Create GitHub Project v2
 * Single responsibility: Create a new GitHub Project v2 via GraphQL API
 */
export async function createProjectV2(octokit, owner, projectData) {
    try {
        // First get the organization/user ID
        const ownerQuery = `
      query GetOwner($login: String!) {
        repositoryOwner(login: $login) {
          id
          __typename
        }
      }
    `;
        const ownerResponse = await octokit.graphql({
            query: ownerQuery,
            login: owner
        });
        const ownerId = ownerResponse.repositoryOwner.id;
        // Create the project
        const mutation = `
      mutation CreateProjectV2($ownerId: ID!, $title: String!) {
        createProjectV2(input: {
          ownerId: $ownerId
          title: $title
        }) {
          projectV2 {
            id
            number
            title
            url
          }
        }
      }
    `;
        const response = await octokit.graphql({
            query: mutation,
            ownerId,
            title: projectData.title
        });
        const project = response.createProjectV2.projectV2;
        return {
            success: true,
            data: {
                id: project.id,
                number: project.number,
                title: project.title,
                url: project.url
            }
        };
    }
    catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}
