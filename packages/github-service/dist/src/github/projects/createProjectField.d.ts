/**
 * Create custom field in GitHub Project V2
 * Single responsibility: Create project fields with options
 */
import type { Octokit } from '@octokit/rest';
import type { Result } from '../types';
export interface ProjectFieldOption {
    name: string;
    color?: 'GRAY' | 'BLUE' | 'GREEN' | 'YELLOW' | 'ORANGE' | 'RED' | 'PINK' | 'PURPLE';
    description?: string;
}
export interface ProjectFieldData {
    projectId: string;
    name: string;
    dataType: 'TEXT' | 'NUMBER' | 'DATE' | 'SINGLE_SELECT' | 'ITERATION';
    options?: ProjectFieldOption[];
}
export interface ProjectFieldResponse {
    id: string;
    name: string;
    dataType: string;
    options?: Array<{
        id: string;
        name: string;
        color?: string;
    }>;
}
/**
 * Create AI Teammate field with predefined options
 */
export declare function createAITeammateField(octokit: Octokit, projectId: string): Promise<Result<ProjectFieldResponse>>;
/**
 * Create custom field in GitHub Project V2
 */
export declare function createProjectField(octokit: Octokit, fieldData: ProjectFieldData): Promise<Result<ProjectFieldResponse>>;
/**
 * Get all fields for a project
 */
export declare function getProjectFields(octokit: Octokit, projectId: string): Promise<Result<ProjectFieldResponse[]>>;
/**
 * Find AI Teammate field in project
 */
export declare function findAITeammateField(octokit: Octokit, projectId: string): Promise<Result<ProjectFieldResponse | null>>;
//# sourceMappingURL=createProjectField.d.ts.map