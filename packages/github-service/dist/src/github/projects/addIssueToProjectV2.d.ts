/**
 * Add issue to GitHub Project v2
 * Single responsibility: Add an issue to a GitHub Project v2 via GraphQL API
 */
import type { Octokit } from '@octokit/rest';
import type { Result } from '../types';
export interface ProjectItemResult {
    id: string;
    projectId: string;
    issueId: string;
}
export declare function addIssueToProjectV2(octokit: Octokit, projectId: string, issueNodeId: string): Promise<Result<ProjectItemResult>>;
//# sourceMappingURL=addIssueToProjectV2.d.ts.map