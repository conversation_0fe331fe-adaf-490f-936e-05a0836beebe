/**
 * Test setup for GitHub Service
 * Loads environment variables and validates configuration
 */
declare const centralizedConfig: import("./config/test-config.js").TestConfig;
export declare const testConfig: {
    hasRealCredentials: boolean | "" | undefined;
    dopplerToken: string;
    organization: string;
    repository: string;
    expectedInstallationId: number | undefined;
    testEpicNumber: number | undefined;
    testFeatureNumber: number | undefined;
    testTaskNumber: number | undefined;
};
export { centralizedConfig };
//# sourceMappingURL=setup.d.ts.map