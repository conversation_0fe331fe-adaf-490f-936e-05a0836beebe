/**
 * Centralized test configuration
 * All test settings in one place - no hardcoded values in test files
 */
export interface TestConfig {
    organization: string;
    repository: string;
    project: {
        title: string;
        description: string;
    };
    timeouts: {
        setup: number;
        standard: number;
        integration: number;
    };
    branches: {
        default: string;
        fallback: string;
    };
    labels: {
        test: string[];
        epic: string[];
        feature: string[];
        task: string[];
    };
}
/**
 * Load test configuration from environment variables with sensible defaults
 */
export declare function loadTestConfig(): TestConfig;
/**
 * Get the working repository for tests
 */
export declare function getWorkingRepo(config: TestConfig): string;
/**
 * Get timeout for specific test type
 */
export declare function getTimeout(config: TestConfig, type: 'setup' | 'standard' | 'integration'): number;
/**
 * Get labels for specific issue type
 */
export declare function getLabels(config: TestConfig, type: 'test' | 'epic' | 'feature' | 'task'): string[];
/**
 * Validate test configuration
 */
export declare function validateTestConfig(config: TestConfig): {
    valid: boolean;
    errors: string[];
};
//# sourceMappingURL=test-config.d.ts.map