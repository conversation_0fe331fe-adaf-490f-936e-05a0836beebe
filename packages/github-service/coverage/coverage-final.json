{"/home/<USER>/ideas/AISDLC/packages/github-service/src/compositions/createEpic.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/compositions/createEpic.ts", "statementMap": {"0": {"start": {"line": 41, "column": 2}, "end": {"line": 112, "column": 3}}, "1": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 55}}, "2": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 76}}, "3": {"start": {"line": 46, "column": 29}, "end": {"line": 46, "column": 67}}, "4": {"start": {"line": 47, "column": 4}, "end": {"line": 51, "column": 5}}, "5": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 80}}, "6": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 92}}, "7": {"start": {"line": 54, "column": 19}, "end": {"line": 54, "column": 47}}, "8": {"start": {"line": 55, "column": 4}, "end": {"line": 57, "column": 5}}, "9": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 26}}, "10": {"start": {"line": 60, "column": 24}, "end": {"line": 63, "column": 6}}, "11": {"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": 5}}, "12": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 25}}, "13": {"start": {"line": 69, "column": 18}, "end": {"line": 69, "column": 34}}, "14": {"start": {"line": 72, "column": 23}, "end": {"line": 72, "column": 91}}, "15": {"start": {"line": 73, "column": 4}, "end": {"line": 78, "column": 5}}, "16": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 49}}, "17": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 77}}, "18": {"start": {"line": 81, "column": 23}, "end": {"line": 81, "column": 73}}, "19": {"start": {"line": 82, "column": 25}, "end": {"line": 82, "column": 97}}, "20": {"start": {"line": 85, "column": 4}, "end": {"line": 90, "column": 5}}, "21": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 79}}, "22": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 39}}, "23": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 79}}, "24": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 53}}, "25": {"start": {"line": 94, "column": 4}, "end": {"line": 105, "column": 6}}, "26": {"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 61}}, "27": {"start": {"line": 108, "column": 4}, "end": {"line": 111, "column": 6}}}, "fnMap": {"0": {"name": "createEpic", "decl": {"start": {"line": 35, "column": 22}, "end": {"line": 35, "column": 32}}, "loc": {"start": {"line": 39, "column": 20}, "end": {"line": 113, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 47, "column": 4}, "end": {"line": 51, "column": 5}}, "type": "if", "locations": [{"start": {"line": 47, "column": 4}, "end": {"line": 51, "column": 5}}, {"start": {"line": 49, "column": 11}, "end": {"line": 51, "column": 5}}]}, "1": {"loc": {"start": {"line": 54, "column": 24}, "end": {"line": 54, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 54, "column": 24}, "end": {"line": 54, "column": 39}}, {"start": {"line": 54, "column": 43}, "end": {"line": 54, "column": 45}}]}, "2": {"loc": {"start": {"line": 55, "column": 4}, "end": {"line": 57, "column": 5}}, "type": "if", "locations": [{"start": {"line": 55, "column": 4}, "end": {"line": 57, "column": 5}}]}, "3": {"loc": {"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": 5}}, "type": "if", "locations": [{"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": 5}}]}, "4": {"loc": {"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 28}}, {"start": {"line": 65, "column": 32}, "end": {"line": 65, "column": 49}}]}, "5": {"loc": {"start": {"line": 73, "column": 4}, "end": {"line": 78, "column": 5}}, "type": "if", "locations": [{"start": {"line": 73, "column": 4}, "end": {"line": 78, "column": 5}}, {"start": {"line": 75, "column": 11}, "end": {"line": 78, "column": 5}}]}, "6": {"loc": {"start": {"line": 85, "column": 4}, "end": {"line": 90, "column": 5}}, "type": "if", "locations": [{"start": {"line": 85, "column": 4}, "end": {"line": 90, "column": 5}}, {"start": {"line": 88, "column": 11}, "end": {"line": 90, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/compositions/createFeature.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/compositions/createFeature.ts", "statementMap": {"0": {"start": {"line": 43, "column": 2}, "end": {"line": 135, "column": 3}}, "1": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 61}}, "2": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 76}}, "3": {"start": {"line": 48, "column": 29}, "end": {"line": 48, "column": 67}}, "4": {"start": {"line": 49, "column": 4}, "end": {"line": 53, "column": 5}}, "5": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 80}}, "6": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 92}}, "7": {"start": {"line": 56, "column": 19}, "end": {"line": 56, "column": 50}}, "8": {"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": 5}}, "9": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 29}}, "10": {"start": {"line": 62, "column": 24}, "end": {"line": 65, "column": 6}}, "11": {"start": {"line": 67, "column": 4}, "end": {"line": 69, "column": 5}}, "12": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 25}}, "13": {"start": {"line": 71, "column": 18}, "end": {"line": 71, "column": 34}}, "14": {"start": {"line": 74, "column": 23}, "end": {"line": 74, "column": 94}}, "15": {"start": {"line": 75, "column": 4}, "end": {"line": 80, "column": 5}}, "16": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 55}}, "17": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 80}}, "18": {"start": {"line": 83, "column": 4}, "end": {"line": 97, "column": 5}}, "19": {"start": {"line": 84, "column": 29}, "end": {"line": 89, "column": null}}, "20": {"start": {"line": 92, "column": 6}, "end": {"line": 96, "column": 7}}, "21": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 81}}, "22": {"start": {"line": 95, "column": 8}, "end": {"line": 95, "column": 75}}, "23": {"start": {"line": 100, "column": 23}, "end": {"line": 103, "column": 6}}, "24": {"start": {"line": 104, "column": 25}, "end": {"line": 104, "column": 97}}, "25": {"start": {"line": 107, "column": 4}, "end": {"line": 112, "column": 5}}, "26": {"start": {"line": 108, "column": 6}, "end": {"line": 108, "column": 79}}, "27": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 39}}, "28": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 79}}, "29": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 56}}, "30": {"start": {"line": 116, "column": 4}, "end": {"line": 128, "column": 6}}, "31": {"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": 64}}, "32": {"start": {"line": 131, "column": 4}, "end": {"line": 134, "column": 6}}}, "fnMap": {"0": {"name": "createFeature", "decl": {"start": {"line": 37, "column": 22}, "end": {"line": 37, "column": 35}}, "loc": {"start": {"line": 41, "column": 26}, "end": {"line": 136, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 49, "column": 4}, "end": {"line": 53, "column": 5}}, "type": "if", "locations": [{"start": {"line": 49, "column": 4}, "end": {"line": 53, "column": 5}}, {"start": {"line": 51, "column": 11}, "end": {"line": 53, "column": 5}}]}, "1": {"loc": {"start": {"line": 56, "column": 24}, "end": {"line": 56, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 56, "column": 24}, "end": {"line": 56, "column": 42}}, {"start": {"line": 56, "column": 46}, "end": {"line": 56, "column": 48}}]}, "2": {"loc": {"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": 5}}, "type": "if", "locations": [{"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": 5}}]}, "3": {"loc": {"start": {"line": 67, "column": 4}, "end": {"line": 69, "column": 5}}, "type": "if", "locations": [{"start": {"line": 67, "column": 4}, "end": {"line": 69, "column": 5}}]}, "4": {"loc": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 28}}, {"start": {"line": 67, "column": 32}, "end": {"line": 67, "column": 49}}]}, "5": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 80, "column": 5}}, "type": "if", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 80, "column": 5}}, {"start": {"line": 77, "column": 11}, "end": {"line": 80, "column": 5}}]}, "6": {"loc": {"start": {"line": 83, "column": 4}, "end": {"line": 97, "column": 5}}, "type": "if", "locations": [{"start": {"line": 83, "column": 4}, "end": {"line": 97, "column": 5}}]}, "7": {"loc": {"start": {"line": 92, "column": 6}, "end": {"line": 96, "column": 7}}, "type": "if", "locations": [{"start": {"line": 92, "column": 6}, "end": {"line": 96, "column": 7}}, {"start": {"line": 94, "column": 13}, "end": {"line": 96, "column": 7}}]}, "8": {"loc": {"start": {"line": 107, "column": 4}, "end": {"line": 112, "column": 5}}, "type": "if", "locations": [{"start": {"line": 107, "column": 4}, "end": {"line": 112, "column": 5}}, {"start": {"line": 110, "column": 11}, "end": {"line": 112, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0], "7": [0, 0], "8": [0, 0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/compositions/createTask.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/compositions/createTask.ts", "statementMap": {"0": {"start": {"line": 42, "column": 2}, "end": {"line": 131, "column": 3}}, "1": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 55}}, "2": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 76}}, "3": {"start": {"line": 47, "column": 29}, "end": {"line": 47, "column": 67}}, "4": {"start": {"line": 48, "column": 4}, "end": {"line": 52, "column": 5}}, "5": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 80}}, "6": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 92}}, "7": {"start": {"line": 55, "column": 19}, "end": {"line": 55, "column": 47}}, "8": {"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}, "9": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 26}}, "10": {"start": {"line": 61, "column": 24}, "end": {"line": 64, "column": 6}}, "11": {"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}, "12": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 25}}, "13": {"start": {"line": 70, "column": 18}, "end": {"line": 70, "column": 34}}, "14": {"start": {"line": 73, "column": 23}, "end": {"line": 73, "column": 91}}, "15": {"start": {"line": 74, "column": 4}, "end": {"line": 79, "column": 5}}, "16": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 49}}, "17": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 77}}, "18": {"start": {"line": 82, "column": 4}, "end": {"line": 96, "column": 5}}, "19": {"start": {"line": 83, "column": 29}, "end": {"line": 88, "column": null}}, "20": {"start": {"line": 91, "column": 6}, "end": {"line": 95, "column": 7}}, "21": {"start": {"line": 92, "column": 8}, "end": {"line": 92, "column": 81}}, "22": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 78}}, "23": {"start": {"line": 99, "column": 23}, "end": {"line": 99, "column": 73}}, "24": {"start": {"line": 100, "column": 25}, "end": {"line": 100, "column": 97}}, "25": {"start": {"line": 103, "column": 4}, "end": {"line": 108, "column": 5}}, "26": {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 79}}, "27": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 39}}, "28": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 79}}, "29": {"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": 53}}, "30": {"start": {"line": 112, "column": 4}, "end": {"line": 124, "column": 6}}, "31": {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": 61}}, "32": {"start": {"line": 127, "column": 4}, "end": {"line": 130, "column": 6}}}, "fnMap": {"0": {"name": "createTask", "decl": {"start": {"line": 36, "column": 22}, "end": {"line": 36, "column": 32}}, "loc": {"start": {"line": 40, "column": 20}, "end": {"line": 132, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 48, "column": 4}, "end": {"line": 52, "column": 5}}, "type": "if", "locations": [{"start": {"line": 48, "column": 4}, "end": {"line": 52, "column": 5}}, {"start": {"line": 50, "column": 11}, "end": {"line": 52, "column": 5}}]}, "1": {"loc": {"start": {"line": 55, "column": 24}, "end": {"line": 55, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 55, "column": 24}, "end": {"line": 55, "column": 39}}, {"start": {"line": 55, "column": 43}, "end": {"line": 55, "column": 45}}]}, "2": {"loc": {"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}, "type": "if", "locations": [{"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}]}, "3": {"loc": {"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}, "type": "if", "locations": [{"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}]}, "4": {"loc": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 28}}, {"start": {"line": 66, "column": 32}, "end": {"line": 66, "column": 49}}]}, "5": {"loc": {"start": {"line": 74, "column": 4}, "end": {"line": 79, "column": 5}}, "type": "if", "locations": [{"start": {"line": 74, "column": 4}, "end": {"line": 79, "column": 5}}, {"start": {"line": 76, "column": 11}, "end": {"line": 79, "column": 5}}]}, "6": {"loc": {"start": {"line": 82, "column": 4}, "end": {"line": 96, "column": 5}}, "type": "if", "locations": [{"start": {"line": 82, "column": 4}, "end": {"line": 96, "column": 5}}]}, "7": {"loc": {"start": {"line": 91, "column": 6}, "end": {"line": 95, "column": 7}}, "type": "if", "locations": [{"start": {"line": 91, "column": 6}, "end": {"line": 95, "column": 7}}, {"start": {"line": 93, "column": 13}, "end": {"line": 95, "column": 7}}]}, "8": {"loc": {"start": {"line": 103, "column": 4}, "end": {"line": 108, "column": 5}}, "type": "if", "locations": [{"start": {"line": 103, "column": 4}, "end": {"line": 108, "column": 5}}, {"start": {"line": 106, "column": 11}, "end": {"line": 108, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0], "7": [0, 0], "8": [0, 0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/branches/createBranch.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/branches/createBranch.ts", "statementMap": {"0": {"start": {"line": 15, "column": 2}, "end": {"line": 36, "column": 3}}, "1": {"start": {"line": 16, "column": 21}, "end": {"line": 21, "column": 6}}, "2": {"start": {"line": 23, "column": 4}, "end": {"line": 30, "column": 6}}, "3": {"start": {"line": 32, "column": 4}, "end": {"line": 35, "column": 6}}}, "fnMap": {"0": {"name": "createBranch", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 34}}, "loc": {"start": {"line": 13, "column": 24}, "end": {"line": 37, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/branches/createLinkedBranch.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/branches/createLinkedBranch.ts", "statementMap": {"0": {"start": {"line": 18, "column": 2}, "end": {"line": 158, "column": 3}}, "1": {"start": {"line": 20, "column": 26}, "end": {"line": 24, "column": 6}}, "2": {"start": {"line": 26, "column": 24}, "end": {"line": 26, "column": 50}}, "3": {"start": {"line": 29, "column": 25}, "end": {"line": 32, "column": 6}}, "4": {"start": {"line": 34, "column": 23}, "end": {"line": 34, "column": 48}}, "5": {"start": {"line": 35, "column": 26}, "end": {"line": 35, "column": 58}}, "6": {"start": {"line": 38, "column": 27}, "end": {"line": 42, "column": 6}}, "7": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 47}}, "8": {"start": {"line": 47, "column": 21}, "end": {"line": 66, "column": 6}}, "9": {"start": {"line": 68, "column": 21}, "end": {"line": 73, "column": 6}}, "10": {"start": {"line": 75, "column": 26}, "end": {"line": 75, "column": 75}}, "11": {"start": {"line": 77, "column": 4}, "end": {"line": 109, "column": 5}}, "12": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 74}}, "13": {"start": {"line": 80, "column": 6}, "end": {"line": 103, "column": 7}}, "14": {"start": {"line": 81, "column": 33}, "end": {"line": 81, "column": 63}}, "15": {"start": {"line": 82, "column": 31}, "end": {"line": 85, "column": 10}}, "16": {"start": {"line": 87, "column": 8}, "end": {"line": 100, "column": 9}}, "17": {"start": {"line": 88, "column": 10}, "end": {"line": 88, "column": 81}}, "18": {"start": {"line": 89, "column": 10}, "end": {"line": 97, "column": 12}}, "19": {"start": {"line": 99, "column": 10}, "end": {"line": 99, "column": 86}}, "20": {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 84}}, "21": {"start": {"line": 105, "column": 6}, "end": {"line": 108, "column": 8}}, "22": {"start": {"line": 111, "column": 4}, "end": {"line": 119, "column": 6}}, "23": {"start": {"line": 122, "column": 4}, "end": {"line": 152, "column": 5}}, "24": {"start": {"line": 124, "column": 27}, "end": {"line": 124, "column": 72}}, "25": {"start": {"line": 125, "column": 28}, "end": {"line": 125, "column": 60}}, "26": {"start": {"line": 126, "column": 29}, "end": {"line": 130, "column": 8}}, "27": {"start": {"line": 131, "column": 32}, "end": {"line": 131, "column": 62}}, "28": {"start": {"line": 133, "column": 31}, "end": {"line": 133, "column": 61}}, "29": {"start": {"line": 134, "column": 29}, "end": {"line": 137, "column": 8}}, "30": {"start": {"line": 139, "column": 6}, "end": {"line": 149, "column": 7}}, "31": {"start": {"line": 140, "column": 8}, "end": {"line": 148, "column": 10}}, "32": {"start": {"line": 154, "column": 4}, "end": {"line": 157, "column": 6}}}, "fnMap": {"0": {"name": "createLinkedBranch", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 40}}, "loc": {"start": {"line": 14, "column": 20}, "end": {"line": 159, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 77, "column": 4}, "end": {"line": 109, "column": 5}}, "type": "if", "locations": [{"start": {"line": 77, "column": 4}, "end": {"line": 109, "column": 5}}]}, "1": {"loc": {"start": {"line": 77, "column": 8}, "end": {"line": 77, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 77, "column": 8}, "end": {"line": 77, "column": 21}}, {"start": {"line": 77, "column": 25}, "end": {"line": 77, "column": 42}}]}, "2": {"loc": {"start": {"line": 87, "column": 8}, "end": {"line": 100, "column": 9}}, "type": "if", "locations": [{"start": {"line": 87, "column": 8}, "end": {"line": 100, "column": 9}}, {"start": {"line": 98, "column": 15}, "end": {"line": 100, "column": 9}}]}, "3": {"loc": {"start": {"line": 139, "column": 6}, "end": {"line": 149, "column": 7}}, "type": "if", "locations": [{"start": {"line": 139, "column": 6}, "end": {"line": 149, "column": 7}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0}, "f": {"0": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/branches/getBranch.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/branches/getBranch.ts", "statementMap": {"0": {"start": {"line": 15, "column": 2}, "end": {"line": 35, "column": 3}}, "1": {"start": {"line": 16, "column": 21}, "end": {"line": 20, "column": 6}}, "2": {"start": {"line": 22, "column": 4}, "end": {"line": 29, "column": 6}}, "3": {"start": {"line": 31, "column": 4}, "end": {"line": 34, "column": 6}}}, "fnMap": {"0": {"name": "getBranch", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 31}}, "loc": {"start": {"line": 13, "column": 20}, "end": {"line": 36, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/comments/createComment.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/comments/createComment.ts", "statementMap": {"0": {"start": {"line": 16, "column": 2}, "end": {"line": 38, "column": 3}}, "1": {"start": {"line": 17, "column": 21}, "end": {"line": 22, "column": 6}}, "2": {"start": {"line": 24, "column": 4}, "end": {"line": 32, "column": 6}}, "3": {"start": {"line": 34, "column": 4}, "end": {"line": 37, "column": 6}}}, "fnMap": {"0": {"name": "createComment", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 35}}, "loc": {"start": {"line": 14, "column": 26}, "end": {"line": 39, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 29, "column": 14}, "end": {"line": 29, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 29, "column": 14}, "end": {"line": 29, "column": 32}}, {"start": {"line": 29, "column": 36}, "end": {"line": 29, "column": 38}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/addIssueLabel.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/addIssueLabel.ts", "statementMap": {"0": {"start": {"line": 16, "column": 2}, "end": {"line": 30, "column": 3}}, "1": {"start": {"line": 17, "column": 4}, "end": {"line": 22, "column": 7}}, "2": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 29}}, "3": {"start": {"line": 26, "column": 4}, "end": {"line": 29, "column": 6}}}, "fnMap": {"0": {"name": "addIssueLabel", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 35}}, "loc": {"start": {"line": 14, "column": 15}, "end": {"line": 31, "column": 1}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 0, "3": 1}, "f": {"0": 1}, "b": {}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/addSubIssue.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/addSubIssue.ts", "statementMap": {"0": {"start": {"line": 16, "column": 2}, "end": {"line": 60, "column": 3}}, "1": {"start": {"line": 18, "column": 23}, "end": {"line": 22, "column": 6}}, "2": {"start": {"line": 24, "column": 4}, "end": {"line": 54, "column": 5}}, "3": {"start": {"line": 26, "column": 6}, "end": {"line": 34, "column": 9}}, "4": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 31}}, "5": {"start": {"line": 39, "column": 6}, "end": {"line": 44, "column": 9}}, "6": {"start": {"line": 46, "column": 6}, "end": {"line": 51, "column": 9}}, "7": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 31}}, "8": {"start": {"line": 56, "column": 4}, "end": {"line": 59, "column": 6}}}, "fnMap": {"0": {"name": "addSubIssue", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 33}}, "loc": {"start": {"line": 14, "column": 26}, "end": {"line": 61, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/createIssue.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/createIssue.ts", "statementMap": {"0": {"start": {"line": 15, "column": 2}, "end": {"line": 42, "column": 3}}, "1": {"start": {"line": 16, "column": 21}, "end": {"line": 24, "column": 6}}, "2": {"start": {"line": 26, "column": 4}, "end": {"line": 36, "column": 6}}, "3": {"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 6}}}, "fnMap": {"0": {"name": "createIssue", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 33}}, "loc": {"start": {"line": 13, "column": 28}, "end": {"line": 43, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 12}, "end": {"line": 20, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 12}, "end": {"line": 20, "column": 26}}, {"start": {"line": 20, "column": 30}, "end": {"line": 20, "column": 32}}]}, "1": {"loc": {"start": {"line": 21, "column": 14}, "end": {"line": 21, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 14}, "end": {"line": 21, "column": 30}}, {"start": {"line": 21, "column": 34}, "end": {"line": 21, "column": 36}}]}, "2": {"loc": {"start": {"line": 22, "column": 17}, "end": {"line": 22, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 22, "column": 17}, "end": {"line": 22, "column": 36}}, {"start": {"line": 22, "column": 40}, "end": {"line": 22, "column": 42}}]}}, "s": {"0": 1, "1": 1, "2": 0, "3": 1}, "f": {"0": 1}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/createIssueType.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/createIssueType.ts", "statementMap": {"0": {"start": {"line": 35, "column": 2}, "end": {"line": 57, "column": 3}}, "1": {"start": {"line": 36, "column": 21}, "end": {"line": 46, "column": 6}}, "2": {"start": {"line": 48, "column": 4}, "end": {"line": 51, "column": 6}}, "3": {"start": {"line": 53, "column": 4}, "end": {"line": 56, "column": 6}}, "4": {"start": {"line": 67, "column": 2}, "end": {"line": 85, "column": 3}}, "5": {"start": {"line": 68, "column": 21}, "end": {"line": 74, "column": 6}}, "6": {"start": {"line": 76, "column": 4}, "end": {"line": 79, "column": 6}}, "7": {"start": {"line": 81, "column": 4}, "end": {"line": 84, "column": 6}}, "8": {"start": {"line": 95, "column": 2}, "end": {"line": 164, "column": 3}}, "9": {"start": {"line": 97, "column": 27}, "end": {"line": 97, "column": 72}}, "10": {"start": {"line": 99, "column": 49}, "end": {"line": 99, "column": 51}}, "11": {"start": {"line": 101, "column": 4}, "end": {"line": 106, "column": 5}}, "12": {"start": {"line": 103, "column": 6}, "end": {"line": 105, "column": 9}}, "13": {"start": {"line": 104, "column": 8}, "end": {"line": 104, "column": 47}}, "14": {"start": {"line": 109, "column": 43}, "end": {"line": 140, "column": 6}}, "15": {"start": {"line": 143, "column": 4}, "end": {"line": 153, "column": 5}}, "16": {"start": {"line": 144, "column": 6}, "end": {"line": 152, "column": 7}}, "17": {"start": {"line": 145, "column": 29}, "end": {"line": 145, "column": 74}}, "18": {"start": {"line": 146, "column": 8}, "end": {"line": 151, "column": 9}}, "19": {"start": {"line": 147, "column": 10}, "end": {"line": 147, "column": 67}}, "20": {"start": {"line": 148, "column": 10}, "end": {"line": 148, "column": 64}}, "21": {"start": {"line": 150, "column": 10}, "end": {"line": 150, "column": 96}}, "22": {"start": {"line": 155, "column": 4}, "end": {"line": 158, "column": 6}}, "23": {"start": {"line": 160, "column": 4}, "end": {"line": 163, "column": 6}}}, "fnMap": {"0": {"name": "createIssueType", "decl": {"start": {"line": 30, "column": 22}, "end": {"line": 30, "column": 37}}, "loc": {"start": {"line": 33, "column": 30}, "end": {"line": 58, "column": 1}}}, "1": {"name": "getOrganizationIssueTypes", "decl": {"start": {"line": 63, "column": 22}, "end": {"line": 63, "column": 47}}, "loc": {"start": {"line": 65, "column": 13}, "end": {"line": 86, "column": 1}}}, "2": {"name": "ensureIssueTypes", "decl": {"start": {"line": 91, "column": 22}, "end": {"line": 91, "column": 38}}, "loc": {"start": {"line": 93, "column": 13}, "end": {"line": 165, "column": 1}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 103, "column": 35}, "end": {"line": 103, "column": 39}}, "loc": {"start": {"line": 103, "column": 42}, "end": {"line": 105, "column": 7}}}}, "branchMap": {"0": {"loc": {"start": {"line": 39, "column": 19}, "end": {"line": 39, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 39, "column": 19}, "end": {"line": 39, "column": 44}}, {"start": {"line": 39, "column": 48}, "end": {"line": 39, "column": 52}}]}, "1": {"loc": {"start": {"line": 40, "column": 13}, "end": {"line": 40, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 13}, "end": {"line": 40, "column": 32}}, {"start": {"line": 40, "column": 36}, "end": {"line": 40, "column": 40}}]}, "2": {"loc": {"start": {"line": 41, "column": 18}, "end": {"line": 41, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 18}, "end": {"line": 41, "column": 42}}, {"start": {"line": 41, "column": 46}, "end": {"line": 41, "column": 50}}]}, "3": {"loc": {"start": {"line": 101, "column": 4}, "end": {"line": 106, "column": 5}}, "type": "if", "locations": [{"start": {"line": 101, "column": 4}, "end": {"line": 106, "column": 5}}]}, "4": {"loc": {"start": {"line": 144, "column": 6}, "end": {"line": 152, "column": 7}}, "type": "if", "locations": [{"start": {"line": 144, "column": 6}, "end": {"line": 152, "column": 7}}]}, "5": {"loc": {"start": {"line": 146, "column": 8}, "end": {"line": 151, "column": 9}}, "type": "if", "locations": [{"start": {"line": 146, "column": 8}, "end": {"line": 151, "column": 9}}, {"start": {"line": 149, "column": 15}, "end": {"line": 151, "column": 9}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0], "4": [0], "5": [0, 0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/getIssue.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/getIssue.ts", "statementMap": {"0": {"start": {"line": 15, "column": 2}, "end": {"line": 38, "column": 3}}, "1": {"start": {"line": 16, "column": 21}, "end": {"line": 20, "column": 6}}, "2": {"start": {"line": 22, "column": 4}, "end": {"line": 32, "column": 6}}, "3": {"start": {"line": 34, "column": 4}, "end": {"line": 37, "column": 6}}}, "fnMap": {"0": {"name": "getIssue", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 30}}, "loc": {"start": {"line": 13, "column": 21}, "end": {"line": 39, "column": 1}}}}, "branchMap": {}, "s": {"0": 2, "1": 2, "2": 0, "3": 2}, "f": {"0": 2}, "b": {}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/getIssueTypes.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/getIssueTypes.ts", "statementMap": {"0": {"start": {"line": 20, "column": 2}, "end": {"line": 61, "column": 3}}, "1": {"start": {"line": 21, "column": 18}, "end": {"line": 33, "column": 6}}, "2": {"start": {"line": 35, "column": 21}, "end": {"line": 42, "column": 6}}, "3": {"start": {"line": 44, "column": 24}, "end": {"line": 44, "column": 76}}, "4": {"start": {"line": 47, "column": 49}, "end": {"line": 47, "column": 51}}, "5": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 7}}, "6": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 40}}, "7": {"start": {"line": 52, "column": 4}, "end": {"line": 55, "column": 6}}, "8": {"start": {"line": 57, "column": 4}, "end": {"line": 60, "column": 6}}}, "fnMap": {"0": {"name": "getIssueTypes", "decl": {"start": {"line": 15, "column": 22}, "end": {"line": 15, "column": 35}}, "loc": {"start": {"line": 18, "column": 14}, "end": {"line": 62, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 48, "column": 23}, "end": {"line": 48, "column": 24}}, "loc": {"start": {"line": 48, "column": 43}, "end": {"line": 50, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 44, "column": 24}, "end": {"line": 44, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 44, "column": 24}, "end": {"line": 44, "column": 70}}, {"start": {"line": 44, "column": 74}, "end": {"line": 44, "column": 76}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/resolveIssueTypes.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/resolveIssueTypes.ts", "statementMap": {"0": {"start": {"line": 24, "column": 23}, "end": {"line": 24, "column": 60}}, "1": {"start": {"line": 34, "column": 2}, "end": {"line": 84, "column": 3}}, "2": {"start": {"line": 35, "column": 21}, "end": {"line": 35, "column": 39}}, "3": {"start": {"line": 38, "column": 4}, "end": {"line": 43, "column": 5}}, "4": {"start": {"line": 39, "column": 6}, "end": {"line": 42, "column": 8}}, "5": {"start": {"line": 46, "column": 27}, "end": {"line": 50, "column": 6}}, "6": {"start": {"line": 52, "column": 46}, "end": {"line": 52, "column": 48}}, "7": {"start": {"line": 55, "column": 4}, "end": {"line": 68, "column": 7}}, "8": {"start": {"line": 56, "column": 24}, "end": {"line": 56, "column": 48}}, "9": {"start": {"line": 57, "column": 6}, "end": {"line": 67, "column": 7}}, "10": {"start": {"line": 58, "column": 8}, "end": {"line": 58, "column": 40}}, "11": {"start": {"line": 59, "column": 13}, "end": {"line": 67, "column": 7}}, "12": {"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 43}}, "13": {"start": {"line": 61, "column": 13}, "end": {"line": 67, "column": 7}}, "14": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 40}}, "15": {"start": {"line": 63, "column": 13}, "end": {"line": 67, "column": 7}}, "16": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 39}}, "17": {"start": {"line": 65, "column": 13}, "end": {"line": 67, "column": 7}}, "18": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 47}}, "19": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 48}}, "20": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 87}}, "21": {"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 6}}, "22": {"start": {"line": 80, "column": 4}, "end": {"line": 83, "column": 6}}, "23": {"start": {"line": 95, "column": 17}, "end": {"line": 95, "column": 62}}, "24": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 36}}, "25": {"start": {"line": 96, "column": 23}, "end": {"line": 96, "column": 36}}, "26": {"start": {"line": 98, "column": 2}, "end": {"line": 98, "column": 46}}, "27": {"start": {"line": 110, "column": 17}, "end": {"line": 110, "column": 62}}, "28": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 40}}, "29": {"start": {"line": 111, "column": 23}, "end": {"line": 111, "column": 40}}, "30": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 32}}, "31": {"start": {"line": 120, "column": 2}, "end": {"line": 120, "column": 25}}}, "fnMap": {"0": {"name": "resolveIssueTypes", "decl": {"start": {"line": 29, "column": 22}, "end": {"line": 29, "column": 39}}, "loc": {"start": {"line": 32, "column": 14}, "end": {"line": 85, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 55, "column": 32}, "end": {"line": 55, "column": 37}}, "loc": {"start": {"line": 55, "column": 40}, "end": {"line": 68, "column": 5}}}, "2": {"name": "hasIssueTypes", "decl": {"start": {"line": 90, "column": 22}, "end": {"line": 90, "column": 35}}, "loc": {"start": {"line": 93, "column": 14}, "end": {"line": 99, "column": 1}}}, "3": {"name": "getIssueTypeId", "decl": {"start": {"line": 104, "column": 22}, "end": {"line": 104, "column": 36}}, "loc": {"start": {"line": 108, "column": 36}, "end": {"line": 114, "column": 1}}}, "4": {"name": "clearIssueTypeCache", "decl": {"start": {"line": 119, "column": 16}, "end": {"line": 119, "column": 35}}, "loc": {"start": {"line": 119, "column": 35}, "end": {"line": 121, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 38, "column": 4}, "end": {"line": 43, "column": 5}}, "type": "if", "locations": [{"start": {"line": 38, "column": 4}, "end": {"line": 43, "column": 5}}]}, "1": {"loc": {"start": {"line": 57, "column": 6}, "end": {"line": 67, "column": 7}}, "type": "if", "locations": [{"start": {"line": 57, "column": 6}, "end": {"line": 67, "column": 7}}, {"start": {"line": 59, "column": 13}, "end": {"line": 67, "column": 7}}]}, "2": {"loc": {"start": {"line": 59, "column": 13}, "end": {"line": 67, "column": 7}}, "type": "if", "locations": [{"start": {"line": 59, "column": 13}, "end": {"line": 67, "column": 7}}, {"start": {"line": 61, "column": 13}, "end": {"line": 67, "column": 7}}]}, "3": {"loc": {"start": {"line": 61, "column": 13}, "end": {"line": 67, "column": 7}}, "type": "if", "locations": [{"start": {"line": 61, "column": 13}, "end": {"line": 67, "column": 7}}, {"start": {"line": 63, "column": 13}, "end": {"line": 67, "column": 7}}]}, "4": {"loc": {"start": {"line": 63, "column": 13}, "end": {"line": 67, "column": 7}}, "type": "if", "locations": [{"start": {"line": 63, "column": 13}, "end": {"line": 67, "column": 7}}, {"start": {"line": 65, "column": 13}, "end": {"line": 67, "column": 7}}]}, "5": {"loc": {"start": {"line": 65, "column": 13}, "end": {"line": 67, "column": 7}}, "type": "if", "locations": [{"start": {"line": 65, "column": 13}, "end": {"line": 67, "column": 7}}]}, "6": {"loc": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 36}}, "type": "if", "locations": [{"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 36}}]}, "7": {"loc": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 40}}, "type": "if", "locations": [{"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 40}}]}}, "s": {"0": 4, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0], "6": [0], "7": [0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/setIssueType.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/setIssueType.ts", "statementMap": {"0": {"start": {"line": 15, "column": 2}, "end": {"line": 48, "column": 3}}, "1": {"start": {"line": 16, "column": 21}, "end": {"line": 31, "column": 6}}, "2": {"start": {"line": 33, "column": 4}, "end": {"line": 40, "column": 7}}, "3": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 29}}, "4": {"start": {"line": 44, "column": 4}, "end": {"line": 47, "column": 6}}, "5": {"start": {"line": 61, "column": 2}, "end": {"line": 102, "column": 3}}, "6": {"start": {"line": 63, "column": 16}, "end": {"line": 63, "column": 21}}, "7": {"start": {"line": 66, "column": 24}, "end": {"line": 66, "column": 60}}, "8": {"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": 5}}, "9": {"start": {"line": 68, "column": 6}, "end": {"line": 71, "column": 8}}, "10": {"start": {"line": 74, "column": 24}, "end": {"line": 74, "column": 51}}, "11": {"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 5}}, "12": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 82}}, "13": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 31}}, "14": {"start": {"line": 81, "column": 26}, "end": {"line": 85, "column": 6}}, "15": {"start": {"line": 87, "column": 24}, "end": {"line": 87, "column": 50}}, "16": {"start": {"line": 90, "column": 19}, "end": {"line": 90, "column": 72}}, "17": {"start": {"line": 92, "column": 4}, "end": {"line": 94, "column": 5}}, "18": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 78}}, "19": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 18}}, "20": {"start": {"line": 98, "column": 4}, "end": {"line": 101, "column": 6}}}, "fnMap": {"0": {"name": "setIssueType", "decl": {"start": {"line": 10, "column": 22}, "end": {"line": 10, "column": 34}}, "loc": {"start": {"line": 13, "column": 21}, "end": {"line": 49, "column": 1}}}, "1": {"name": "setIssueTypeByName", "decl": {"start": {"line": 54, "column": 22}, "end": {"line": 54, "column": 40}}, "loc": {"start": {"line": 59, "column": 18}, "end": {"line": 103, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": 5}}, "type": "if", "locations": [{"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": 5}}]}, "1": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 5}}, "type": "if", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 78, "column": 5}}]}, "2": {"loc": {"start": {"line": 92, "column": 4}, "end": {"line": 94, "column": 5}}, "type": "if", "locations": [{"start": {"line": 92, "column": 4}, "end": {"line": 94, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0], "1": [0], "2": [0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/addIssueToProject.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/addIssueToProject.ts", "statementMap": {"0": {"start": {"line": 16, "column": 2}, "end": {"line": 56, "column": 3}}, "1": {"start": {"line": 18, "column": 26}, "end": {"line": 22, "column": 6}}, "2": {"start": {"line": 24, "column": 24}, "end": {"line": 24, "column": 50}}, "3": {"start": {"line": 27, "column": 28}, "end": {"line": 38, "column": 6}}, "4": {"start": {"line": 40, "column": 24}, "end": {"line": 43, "column": 6}}, "5": {"start": {"line": 45, "column": 20}, "end": {"line": 45, "column": 68}}, "6": {"start": {"line": 47, "column": 4}, "end": {"line": 50, "column": 6}}, "7": {"start": {"line": 52, "column": 4}, "end": {"line": 55, "column": 6}}}, "fnMap": {"0": {"name": "addIssueToProject", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 39}}, "loc": {"start": {"line": 14, "column": 21}, "end": {"line": 57, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/addIssueToProjectV2.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/addIssueToProjectV2.ts", "statementMap": {"0": {"start": {"line": 20, "column": 2}, "end": {"line": 64, "column": 3}}, "1": {"start": {"line": 21, "column": 21}, "end": {"line": 41, "column": 6}}, "2": {"start": {"line": 43, "column": 21}, "end": {"line": 47, "column": 6}}, "3": {"start": {"line": 49, "column": 18}, "end": {"line": 49, "column": 60}}, "4": {"start": {"line": 51, "column": 4}, "end": {"line": 58, "column": 6}}, "5": {"start": {"line": 60, "column": 4}, "end": {"line": 63, "column": 6}}}, "fnMap": {"0": {"name": "addIssueToProjectV2", "decl": {"start": {"line": 15, "column": 22}, "end": {"line": 15, "column": 41}}, "loc": {"start": {"line": 18, "column": 21}, "end": {"line": 65, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/assignAITeammate.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/assignAITeammate.ts", "statementMap": {"0": {"start": {"line": 36, "column": 2}, "end": {"line": 96, "column": 3}}, "1": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 73}}, "2": {"start": {"line": 40, "column": 24}, "end": {"line": 40, "column": 69}}, "3": {"start": {"line": 41, "column": 4}, "end": {"line": 46, "column": 5}}, "4": {"start": {"line": 42, "column": 6}, "end": {"line": 45, "column": 8}}, "5": {"start": {"line": 48, "column": 18}, "end": {"line": 48, "column": 34}}, "6": {"start": {"line": 51, "column": 19}, "end": {"line": 51, "column": 70}}, "7": {"start": {"line": 51, "column": 46}, "end": {"line": 51, "column": 69}}, "8": {"start": {"line": 52, "column": 4}, "end": {"line": 57, "column": 5}}, "9": {"start": {"line": 53, "column": 6}, "end": {"line": 56, "column": 8}}, "10": {"start": {"line": 60, "column": 21}, "end": {"line": 75, "column": 6}}, "11": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 36}}, "12": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 60}}, "13": {"start": {"line": 81, "column": 4}, "end": {"line": 89, "column": 6}}, "14": {"start": {"line": 91, "column": 4}, "end": {"line": 91, "column": 68}}, "15": {"start": {"line": 92, "column": 4}, "end": {"line": 95, "column": 6}}, "16": {"start": {"line": 107, "column": 2}, "end": {"line": 200, "column": 3}}, "17": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 85}}, "18": {"start": {"line": 111, "column": 24}, "end": {"line": 111, "column": 69}}, "19": {"start": {"line": 112, "column": 4}, "end": {"line": 117, "column": 5}}, "20": {"start": {"line": 113, "column": 6}, "end": {"line": 116, "column": 8}}, "21": {"start": {"line": 119, "column": 18}, "end": {"line": 119, "column": 34}}, "22": {"start": {"line": 120, "column": 19}, "end": {"line": 120, "column": 70}}, "23": {"start": {"line": 120, "column": 46}, "end": {"line": 120, "column": 69}}, "24": {"start": {"line": 121, "column": 4}, "end": {"line": 126, "column": 5}}, "25": {"start": {"line": 122, "column": 6}, "end": {"line": 125, "column": 8}}, "26": {"start": {"line": 129, "column": 18}, "end": {"line": 161, "column": 6}}, "27": {"start": {"line": 163, "column": 21}, "end": {"line": 163, "column": 49}}, "28": {"start": {"line": 164, "column": 19}, "end": {"line": 164, "column": 52}}, "29": {"start": {"line": 167, "column": 49}, "end": {"line": 167, "column": 51}}, "30": {"start": {"line": 169, "column": 4}, "end": {"line": 186, "column": 5}}, "31": {"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 34}}, "32": {"start": {"line": 170, "column": 25}, "end": {"line": 170, "column": 34}}, "33": {"start": {"line": 173, "column": 30}, "end": {"line": 174, "column": null}}, "34": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 78}}, "35": {"start": {"line": 177, "column": 6}, "end": {"line": 185, "column": 7}}, "36": {"start": {"line": 178, "column": 8}, "end": {"line": 184, "column": 11}}, "37": {"start": {"line": 188, "column": 4}, "end": {"line": 188, "column": 79}}, "38": {"start": {"line": 190, "column": 4}, "end": {"line": 193, "column": 6}}, "39": {"start": {"line": 195, "column": 4}, "end": {"line": 195, "column": 68}}, "40": {"start": {"line": 196, "column": 4}, "end": {"line": 199, "column": 6}}, "41": {"start": {"line": 210, "column": 2}, "end": {"line": 300, "column": 3}}, "42": {"start": {"line": 211, "column": 4}, "end": {"line": 211, "column": 74}}, "43": {"start": {"line": 214, "column": 24}, "end": {"line": 214, "column": 69}}, "44": {"start": {"line": 215, "column": 4}, "end": {"line": 220, "column": 5}}, "45": {"start": {"line": 216, "column": 6}, "end": {"line": 219, "column": 8}}, "46": {"start": {"line": 222, "column": 18}, "end": {"line": 222, "column": 34}}, "47": {"start": {"line": 225, "column": 18}, "end": {"line": 257, "column": 6}}, "48": {"start": {"line": 259, "column": 21}, "end": {"line": 259, "column": 49}}, "49": {"start": {"line": 260, "column": 19}, "end": {"line": 260, "column": 52}}, "50": {"start": {"line": 263, "column": 49}, "end": {"line": 263, "column": 51}}, "51": {"start": {"line": 265, "column": 4}, "end": {"line": 286, "column": 5}}, "52": {"start": {"line": 266, "column": 6}, "end": {"line": 266, "column": 34}}, "53": {"start": {"line": 266, "column": 25}, "end": {"line": 266, "column": 34}}, "54": {"start": {"line": 269, "column": 30}, "end": {"line": 270, "column": null}}, "55": {"start": {"line": 270, "column": 8}, "end": {"line": 270, "column": 41}}, "56": {"start": {"line": 274, "column": 6}, "end": {"line": 277, "column": 7}}, "57": {"start": {"line": 275, "column": 23}, "end": {"line": 275, "column": 86}}, "58": {"start": {"line": 275, "column": 50}, "end": {"line": 275, "column": 85}}, "59": {"start": {"line": 276, "column": 8}, "end": {"line": 276, "column": 48}}, "60": {"start": {"line": 279, "column": 6}, "end": {"line": 285, "column": 9}}, "61": {"start": {"line": 288, "column": 4}, "end": {"line": 288, "column": 78}}, "62": {"start": {"line": 290, "column": 4}, "end": {"line": 293, "column": 6}}, "63": {"start": {"line": 295, "column": 4}, "end": {"line": 295, "column": 72}}, "64": {"start": {"line": 296, "column": 4}, "end": {"line": 299, "column": 6}}}, "fnMap": {"0": {"name": "assignAITeammate", "decl": {"start": {"line": 30, "column": 22}, "end": {"line": 30, "column": 38}}, "loc": {"start": {"line": 34, "column": 24}, "end": {"line": 97, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 51, "column": 39}, "end": {"line": 51, "column": 42}}, "loc": {"start": {"line": 51, "column": 46}, "end": {"line": 51, "column": 69}}}, "2": {"name": "getAIAssignments", "decl": {"start": {"line": 102, "column": 22}, "end": {"line": 102, "column": 38}}, "loc": {"start": {"line": 105, "column": 24}, "end": {"line": 201, "column": 1}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 120, "column": 39}, "end": {"line": 120, "column": 42}}, "loc": {"start": {"line": 120, "column": 46}, "end": {"line": 120, "column": 69}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 173, "column": 58}, "end": {"line": 173, "column": 59}}, "loc": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 78}}}, "5": {"name": "getAllAIAssignments", "decl": {"start": {"line": 206, "column": 22}, "end": {"line": 206, "column": 41}}, "loc": {"start": {"line": 208, "column": 19}, "end": {"line": 301, "column": 1}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 269, "column": 58}, "end": {"line": 269, "column": 59}}, "loc": {"start": {"line": 270, "column": 8}, "end": {"line": 270, "column": 41}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 275, "column": 43}, "end": {"line": 275, "column": 46}}, "loc": {"start": {"line": 275, "column": 50}, "end": {"line": 275, "column": 85}}}}, "branchMap": {"0": {"loc": {"start": {"line": 41, "column": 4}, "end": {"line": 46, "column": 5}}, "type": "if", "locations": [{"start": {"line": 41, "column": 4}, "end": {"line": 46, "column": 5}}]}, "1": {"loc": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 28}}, {"start": {"line": 41, "column": 32}, "end": {"line": 41, "column": 49}}]}, "2": {"loc": {"start": {"line": 52, "column": 4}, "end": {"line": 57, "column": 5}}, "type": "if", "locations": [{"start": {"line": 52, "column": 4}, "end": {"line": 57, "column": 5}}]}, "3": {"loc": {"start": {"line": 112, "column": 4}, "end": {"line": 117, "column": 5}}, "type": "if", "locations": [{"start": {"line": 112, "column": 4}, "end": {"line": 117, "column": 5}}]}, "4": {"loc": {"start": {"line": 112, "column": 8}, "end": {"line": 112, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 112, "column": 8}, "end": {"line": 112, "column": 28}}, {"start": {"line": 112, "column": 32}, "end": {"line": 112, "column": 49}}]}, "5": {"loc": {"start": {"line": 121, "column": 4}, "end": {"line": 126, "column": 5}}, "type": "if", "locations": [{"start": {"line": 121, "column": 4}, "end": {"line": 126, "column": 5}}]}, "6": {"loc": {"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 34}}, "type": "if", "locations": [{"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 34}}]}, "7": {"loc": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 41}}, {"start": {"line": 174, "column": 45}, "end": {"line": 174, "column": 78}}]}, "8": {"loc": {"start": {"line": 177, "column": 6}, "end": {"line": 185, "column": 7}}, "type": "if", "locations": [{"start": {"line": 177, "column": 6}, "end": {"line": 185, "column": 7}}]}, "9": {"loc": {"start": {"line": 215, "column": 4}, "end": {"line": 220, "column": 5}}, "type": "if", "locations": [{"start": {"line": 215, "column": 4}, "end": {"line": 220, "column": 5}}]}, "10": {"loc": {"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 28}}, {"start": {"line": 215, "column": 32}, "end": {"line": 215, "column": 49}}]}, "11": {"loc": {"start": {"line": 266, "column": 6}, "end": {"line": 266, "column": 34}}, "type": "if", "locations": [{"start": {"line": 266, "column": 6}, "end": {"line": 266, "column": 34}}]}, "12": {"loc": {"start": {"line": 274, "column": 6}, "end": {"line": 277, "column": 7}}, "type": "if", "locations": [{"start": {"line": 274, "column": 6}, "end": {"line": 277, "column": 7}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0], "1": [0, 0], "2": [0], "3": [0], "4": [0, 0], "5": [0], "6": [0], "7": [0, 0], "8": [0], "9": [0], "10": [0, 0], "11": [0], "12": [0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/createProject.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/createProject.ts", "statementMap": {"0": {"start": {"line": 14, "column": 2}, "end": {"line": 59, "column": 3}}, "1": {"start": {"line": 16, "column": 21}, "end": {"line": 31, "column": 6}}, "2": {"start": {"line": 34, "column": 24}, "end": {"line": 34, "column": 67}}, "3": {"start": {"line": 35, "column": 20}, "end": {"line": 35, "column": 44}}, "4": {"start": {"line": 37, "column": 21}, "end": {"line": 40, "column": 6}}, "5": {"start": {"line": 42, "column": 21}, "end": {"line": 42, "column": 63}}, "6": {"start": {"line": 44, "column": 4}, "end": {"line": 53, "column": 6}}, "7": {"start": {"line": 55, "column": 4}, "end": {"line": 58, "column": 6}}}, "fnMap": {"0": {"name": "createProject", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 35}}, "loc": {"start": {"line": 12, "column": 26}, "end": {"line": 60, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/createProjectField.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/createProjectField.ts", "statementMap": {"0": {"start": {"line": 40, "column": 2}, "end": {"line": 99, "column": 3}}, "1": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 65}}, "2": {"start": {"line": 44, "column": 21}, "end": {"line": 76, "column": 6}}, "3": {"start": {"line": 78, "column": 21}, "end": {"line": 78, "column": 52}}, "4": {"start": {"line": 79, "column": 19}, "end": {"line": 79, "column": 71}}, "5": {"start": {"line": 81, "column": 4}, "end": {"line": 81, "column": 89}}, "6": {"start": {"line": 83, "column": 4}, "end": {"line": 91, "column": 6}}, "7": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 74}}, "8": {"start": {"line": 95, "column": 4}, "end": {"line": 98, "column": 6}}, "9": {"start": {"line": 109, "column": 2}, "end": {"line": 161, "column": 3}}, "10": {"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": 64}}, "11": {"start": {"line": 113, "column": 21}, "end": {"line": 139, "column": 6}}, "12": {"start": {"line": 141, "column": 21}, "end": {"line": 141, "column": 52}}, "13": {"start": {"line": 142, "column": 19}, "end": {"line": 142, "column": 71}}, "14": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 72}}, "15": {"start": {"line": 146, "column": 4}, "end": {"line": 154, "column": 6}}, "16": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 70}}, "17": {"start": {"line": 157, "column": 4}, "end": {"line": 160, "column": 6}}, "18": {"start": {"line": 171, "column": 2}, "end": {"line": 222, "column": 3}}, "19": {"start": {"line": 172, "column": 4}, "end": {"line": 172, "column": 63}}, "20": {"start": {"line": 174, "column": 18}, "end": {"line": 200, "column": 6}}, "21": {"start": {"line": 202, "column": 21}, "end": {"line": 202, "column": 49}}, "22": {"start": {"line": 203, "column": 20}, "end": {"line": 203, "column": 54}}, "23": {"start": {"line": 205, "column": 4}, "end": {"line": 205, "column": 59}}, "24": {"start": {"line": 207, "column": 4}, "end": {"line": 215, "column": 6}}, "25": {"start": {"line": 209, "column": 40}, "end": {"line": 214, "column": 8}}, "26": {"start": {"line": 217, "column": 4}, "end": {"line": 217, "column": 68}}, "27": {"start": {"line": 218, "column": 4}, "end": {"line": 221, "column": 6}}, "28": {"start": {"line": 232, "column": 2}, "end": {"line": 252, "column": 3}}, "29": {"start": {"line": 233, "column": 25}, "end": {"line": 233, "column": 67}}, "30": {"start": {"line": 234, "column": 4}, "end": {"line": 239, "column": 5}}, "31": {"start": {"line": 235, "column": 6}, "end": {"line": 238, "column": 8}}, "32": {"start": {"line": 241, "column": 28}, "end": {"line": 241, "column": 89}}, "33": {"start": {"line": 241, "column": 60}, "end": {"line": 241, "column": 88}}, "34": {"start": {"line": 243, "column": 4}, "end": {"line": 246, "column": 6}}, "35": {"start": {"line": 248, "column": 4}, "end": {"line": 251, "column": 6}}}, "fnMap": {"0": {"name": "createAITeammateField", "decl": {"start": {"line": 36, "column": 22}, "end": {"line": 36, "column": 43}}, "loc": {"start": {"line": 38, "column": 19}, "end": {"line": 100, "column": 1}}}, "1": {"name": "createProjectField", "decl": {"start": {"line": 105, "column": 22}, "end": {"line": 105, "column": 40}}, "loc": {"start": {"line": 107, "column": 29}, "end": {"line": 162, "column": 1}}}, "2": {"name": "getProjectFields", "decl": {"start": {"line": 167, "column": 22}, "end": {"line": 167, "column": 38}}, "loc": {"start": {"line": 169, "column": 19}, "end": {"line": 223, "column": 1}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 209, "column": 23}, "end": {"line": 209, "column": 24}}, "loc": {"start": {"line": 209, "column": 40}, "end": {"line": 214, "column": 8}}}, "4": {"name": "findAITeammateField", "decl": {"start": {"line": 228, "column": 22}, "end": {"line": 228, "column": 41}}, "loc": {"start": {"line": 230, "column": 19}, "end": {"line": 253, "column": 1}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 241, "column": 51}, "end": {"line": 241, "column": 56}}, "loc": {"start": {"line": 241, "column": 60}, "end": {"line": 241, "column": 88}}}}, "branchMap": {"0": {"loc": {"start": {"line": 89, "column": 17}, "end": {"line": 89, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 89, "column": 17}, "end": {"line": 89, "column": 30}}, {"start": {"line": 89, "column": 34}, "end": {"line": 89, "column": 36}}]}, "1": {"loc": {"start": {"line": 152, "column": 17}, "end": {"line": 152, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 152, "column": 17}, "end": {"line": 152, "column": 30}}, {"start": {"line": 152, "column": 34}, "end": {"line": 152, "column": 36}}]}, "2": {"loc": {"start": {"line": 213, "column": 17}, "end": {"line": 213, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 213, "column": 17}, "end": {"line": 213, "column": 30}}, {"start": {"line": 213, "column": 34}, "end": {"line": 213, "column": 36}}]}, "3": {"loc": {"start": {"line": 234, "column": 4}, "end": {"line": 239, "column": 5}}, "type": "if", "locations": [{"start": {"line": 234, "column": 4}, "end": {"line": 239, "column": 5}}]}, "4": {"loc": {"start": {"line": 234, "column": 8}, "end": {"line": 234, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 234, "column": 8}, "end": {"line": 234, "column": 29}}, {"start": {"line": 234, "column": 33}, "end": {"line": 234, "column": 51}}]}, "5": {"loc": {"start": {"line": 237, "column": 15}, "end": {"line": 237, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 237, "column": 15}, "end": {"line": 237, "column": 33}}, {"start": {"line": 237, "column": 37}, "end": {"line": 237, "column": 67}}]}, "6": {"loc": {"start": {"line": 245, "column": 12}, "end": {"line": 245, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 245, "column": 12}, "end": {"line": 245, "column": 27}}, {"start": {"line": 245, "column": 31}, "end": {"line": 245, "column": 35}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/createProjectV2.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/createProjectV2.ts", "statementMap": {"0": {"start": {"line": 27, "column": 2}, "end": {"line": 84, "column": 3}}, "1": {"start": {"line": 29, "column": 23}, "end": {"line": 36, "column": 6}}, "2": {"start": {"line": 38, "column": 26}, "end": {"line": 41, "column": 6}}, "3": {"start": {"line": 43, "column": 21}, "end": {"line": 43, "column": 61}}, "4": {"start": {"line": 46, "column": 21}, "end": {"line": 60, "column": 6}}, "5": {"start": {"line": 62, "column": 21}, "end": {"line": 66, "column": 6}}, "6": {"start": {"line": 68, "column": 21}, "end": {"line": 68, "column": 63}}, "7": {"start": {"line": 70, "column": 4}, "end": {"line": 78, "column": 6}}, "8": {"start": {"line": 80, "column": 4}, "end": {"line": 83, "column": 6}}}, "fnMap": {"0": {"name": "createProjectV2", "decl": {"start": {"line": 22, "column": 22}, "end": {"line": 22, "column": 37}}, "loc": {"start": {"line": 25, "column": 26}, "end": {"line": 85, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/repositories/createOrgRepository.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/repositories/createOrgRepository.ts", "statementMap": {"0": {"start": {"line": 14, "column": 2}, "end": {"line": 42, "column": 3}}, "1": {"start": {"line": 15, "column": 21}, "end": {"line": 22, "column": 6}}, "2": {"start": {"line": 24, "column": 4}, "end": {"line": 36, "column": 6}}, "3": {"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 6}}}, "fnMap": {"0": {"name": "createOrgRepository", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 41}}, "loc": {"start": {"line": 12, "column": 38}, "end": {"line": 43, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 45}}, {"start": {"line": 18, "column": 49}, "end": {"line": 18, "column": 51}}]}, "1": {"loc": {"start": {"line": 19, "column": 15}, "end": {"line": 19, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 15}, "end": {"line": 19, "column": 37}}, {"start": {"line": 19, "column": 41}, "end": {"line": 19, "column": 46}}]}, "2": {"loc": {"start": {"line": 20, "column": 17}, "end": {"line": 20, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 17}, "end": {"line": 20, "column": 41}}, {"start": {"line": 20, "column": 45}, "end": {"line": 20, "column": 49}}]}, "3": {"loc": {"start": {"line": 21, "column": 24}, "end": {"line": 21, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 24}, "end": {"line": 21, "column": 55}}, {"start": {"line": 21, "column": 59}, "end": {"line": 21, "column": 64}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/repositories/createRepository.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/repositories/createRepository.ts", "statementMap": {"0": {"start": {"line": 13, "column": 2}, "end": {"line": 40, "column": 3}}, "1": {"start": {"line": 14, "column": 21}, "end": {"line": 20, "column": 6}}, "2": {"start": {"line": 22, "column": 4}, "end": {"line": 34, "column": 6}}, "3": {"start": {"line": 36, "column": 4}, "end": {"line": 39, "column": 6}}}, "fnMap": {"0": {"name": "createRepository", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 38}}, "loc": {"start": {"line": 11, "column": 38}, "end": {"line": 41, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 19}, "end": {"line": 16, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 16, "column": 19}, "end": {"line": 16, "column": 45}}, {"start": {"line": 16, "column": 49}, "end": {"line": 16, "column": 51}}]}, "1": {"loc": {"start": {"line": 17, "column": 15}, "end": {"line": 17, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 17, "column": 15}, "end": {"line": 17, "column": 37}}, {"start": {"line": 17, "column": 41}, "end": {"line": 17, "column": 46}}]}, "2": {"loc": {"start": {"line": 18, "column": 17}, "end": {"line": 18, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 17}, "end": {"line": 18, "column": 41}}, {"start": {"line": 18, "column": 45}, "end": {"line": 18, "column": 49}}]}, "3": {"loc": {"start": {"line": 19, "column": 24}, "end": {"line": 19, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 24}, "end": {"line": 19, "column": 55}}, {"start": {"line": 19, "column": 59}, "end": {"line": 19, "column": 64}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/repositories/getRepository.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/repositories/getRepository.ts", "statementMap": {"0": {"start": {"line": 14, "column": 2}, "end": {"line": 38, "column": 3}}, "1": {"start": {"line": 15, "column": 21}, "end": {"line": 18, "column": 6}}, "2": {"start": {"line": 20, "column": 4}, "end": {"line": 32, "column": 6}}, "3": {"start": {"line": 34, "column": 4}, "end": {"line": 37, "column": 6}}}, "fnMap": {"0": {"name": "getRepository", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 35}}, "loc": {"start": {"line": 12, "column": 14}, "end": {"line": 39, "column": 1}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/repositories/listRepositories.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/repositories/listRepositories.ts", "statementMap": {"0": {"start": {"line": 12, "column": 2}, "end": {"line": 37, "column": 3}}, "1": {"start": {"line": 13, "column": 21}, "end": {"line": 15, "column": 6}}, "2": {"start": {"line": 17, "column": 18}, "end": {"line": 26, "column": 7}}, "3": {"start": {"line": 17, "column": 65}, "end": {"line": 26, "column": 6}}, "4": {"start": {"line": 28, "column": 4}, "end": {"line": 31, "column": 6}}, "5": {"start": {"line": 33, "column": 4}, "end": {"line": 36, "column": 6}}}, "fnMap": {"0": {"name": "listRepositories", "decl": {"start": {"line": 9, "column": 22}, "end": {"line": 9, "column": 38}}, "loc": {"start": {"line": 10, "column": 18}, "end": {"line": 38, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 49}, "end": {"line": 17, "column": 50}}, "loc": {"start": {"line": 17, "column": 65}, "end": {"line": 26, "column": 6}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/utils/generateBranchName.ts": {"path": "/home/<USER>/ideas/AISDLC/packages/github-service/src/github/utils/generateBranchName.ts", "statementMap": {"0": {"start": {"line": 7, "column": 19}, "end": {"line": 7, "column": 105}}, "1": {"start": {"line": 7, "column": 36}, "end": {"line": 7, "column": 105}}, "2": {"start": {"line": 10, "column": 20}, "end": {"line": 10, "column": 51}}, "3": {"start": {"line": 12, "column": 2}, "end": {"line": 21, "column": 3}}, "4": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 86}}, "5": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 90}}, "6": {"start": {"line": 18, "column": 6}, "end": {"line": 18, "column": 90}}, "7": {"start": {"line": 20, "column": 6}, "end": {"line": 20, "column": 63}}}, "fnMap": {"0": {"name": "generateBranchName", "decl": {"start": {"line": 6, "column": 16}, "end": {"line": 6, "column": 34}}, "loc": {"start": {"line": 6, "column": 89}, "end": {"line": 22, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 7, "column": 19}, "end": {"line": 7, "column": 20}}, "loc": {"start": {"line": 7, "column": 36}, "end": {"line": 7, "column": 105}}}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 21, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 13, "column": 4}, "end": {"line": 14, "column": 86}}, {"start": {"line": 15, "column": 4}, "end": {"line": 16, "column": 90}}, {"start": {"line": 17, "column": 4}, "end": {"line": 18, "column": 90}}, {"start": {"line": 19, "column": 4}, "end": {"line": 20, "column": 63}}]}, "1": {"loc": {"start": {"line": 14, "column": 30}, "end": {"line": 14, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 14, "column": 30}, "end": {"line": 14, "column": 50}}, {"start": {"line": 14, "column": 54}, "end": {"line": 14, "column": 69}}]}, "2": {"loc": {"start": {"line": 16, "column": 33}, "end": {"line": 16, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 16, "column": 33}, "end": {"line": 16, "column": 54}}, {"start": {"line": 16, "column": 58}, "end": {"line": 16, "column": 73}}]}}, "s": {"0": 4, "1": 4, "2": 4, "3": 4, "4": 1, "5": 2, "6": 1, "7": 0}, "f": {"0": 4, "1": 4}, "b": {"0": [1, 2, 1, 0], "1": [1, 1], "2": [2, 1]}}}