<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1751362499301" clover="3.2.0">
  <project timestamp="1751362499301" name="All files">
    <metrics statements="394" coveredstatements="16" conditionals="137" coveredconditionals="7" methods="49" coveredmethods="5" elements="580" coveredelements="28" complexity="0" loc="394" ncloc="394" packages="7" files="26" classes="26"/>
    <package name="compositions">
      <metrics statements="94" coveredstatements="0" conditionals="42" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="createEpic.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/compositions/createEpic.ts">
        <metrics statements="28" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
      </file>
      <file name="createFeature.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/compositions/createFeature.ts">
        <metrics statements="33" coveredstatements="0" conditionals="15" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="58" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="84" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
      </file>
      <file name="createTask.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/compositions/createTask.ts">
        <metrics statements="33" coveredstatements="0" conditionals="15" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="57" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="83" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
      </file>
    </package>
    <package name="github.branches">
      <metrics statements="41" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="createBranch.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/branches/createBranch.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
      </file>
      <file name="createLinkedBranch.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/branches/createLinkedBranch.ts">
        <metrics statements="33" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="140" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
      </file>
      <file name="getBranch.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/branches/getBranch.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
      </file>
    </package>
    <package name="github.comments">
      <metrics statements="4" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="createComment.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/comments/createComment.ts">
        <metrics statements="4" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
      </file>
    </package>
    <package name="github.issues">
      <metrics statements="105" coveredstatements="10" conditionals="33" coveredconditionals="0" methods="17" coveredmethods="3"/>
      <file name="addIssueLabel.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/addIssueLabel.ts">
        <metrics statements="4" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
      </file>
      <file name="addSubIssue.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/addSubIssue.ts">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
      </file>
      <file name="createIssue.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/createIssue.ts">
        <metrics statements="4" coveredstatements="3" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
      </file>
      <file name="createIssueType.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/createIssueType.ts">
        <metrics statements="24" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
      </file>
      <file name="getIssue.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/getIssue.ts">
        <metrics statements="4" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="15" count="2" type="stmt"/>
        <line num="16" count="2" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="34" count="2" type="stmt"/>
      </file>
      <file name="getIssueTypes.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/getIssueTypes.ts">
        <metrics statements="9" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
      </file>
      <file name="resolveIssueTypes.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/resolveIssueTypes.ts">
        <metrics statements="30" coveredstatements="1" conditionals="12" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="24" count="4" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="39" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="66" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="98" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="113" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
      </file>
      <file name="setIssueType.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/issues/setIssueType.ts">
        <metrics statements="21" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="68" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
      </file>
    </package>
    <package name="github.projects">
      <metrics statements="126" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="18" coveredmethods="0"/>
      <file name="addIssueToProject.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/addIssueToProject.ts">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
      </file>
      <file name="addIssueToProjectV2.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/addIssueToProjectV2.ts">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
      </file>
      <file name="assignAITeammate.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/assignAITeammate.ts">
        <metrics statements="60" coveredstatements="0" conditionals="17" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="42" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="53" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="113" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="122" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="178" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="216" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
      </file>
      <file name="createProject.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/createProject.ts">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
      </file>
      <file name="createProjectField.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/createProjectField.ts">
        <metrics statements="35" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="235" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
      </file>
      <file name="createProjectV2.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/projects/createProjectV2.ts">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
      </file>
    </package>
    <package name="github.repositories">
      <metrics statements="17" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="5" coveredmethods="0"/>
      <file name="createOrgRepository.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/repositories/createOrgRepository.ts">
        <metrics statements="4" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
      </file>
      <file name="createRepository.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/repositories/createRepository.ts">
        <metrics statements="4" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
      </file>
      <file name="getRepository.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/repositories/getRepository.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
      </file>
      <file name="listRepositories.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/repositories/listRepositories.ts">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
      </file>
    </package>
    <package name="github.utils">
      <metrics statements="7" coveredstatements="6" conditionals="8" coveredconditionals="7" methods="2" coveredmethods="2"/>
      <file name="generateBranchName.ts" path="/home/<USER>/ideas/AISDLC/packages/github-service/src/github/utils/generateBranchName.ts">
        <metrics statements="7" coveredstatements="6" conditionals="8" coveredconditionals="7" methods="2" coveredmethods="2"/>
        <line num="7" count="4" type="stmt"/>
        <line num="10" count="4" type="stmt"/>
        <line num="12" count="4" type="cond" truecount="3" falsecount="1"/>
        <line num="14" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="16" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="18" count="1" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
