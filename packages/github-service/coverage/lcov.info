TN:
SF:src/compositions/createEpic.ts
FN:35,createEpic
FNF:1
FNH:0
FNDA:0,createEpic
DA:41,0
DA:42,0
DA:45,0
DA:46,0
DA:47,0
DA:48,0
DA:50,0
DA:54,0
DA:55,0
DA:56,0
DA:60,0
DA:65,0
DA:66,0
DA:69,0
DA:72,0
DA:73,0
DA:74,0
DA:77,0
DA:81,0
DA:82,0
DA:85,0
DA:86,0
DA:87,0
DA:89,0
DA:92,0
DA:94,0
DA:107,0
DA:108,0
LF:28
LH:0
BRDA:47,0,0,0
BRDA:47,0,1,0
BRDA:54,1,0,0
BRDA:54,1,1,0
BRDA:55,2,0,0
BRDA:65,3,0,0
BRDA:65,4,0,0
BRDA:65,4,1,0
BRDA:73,5,0,0
BRDA:73,5,1,0
BRDA:85,6,0,0
BRDA:85,6,1,0
BRF:12
BRH:0
end_of_record
TN:
SF:src/compositions/createFeature.ts
FN:37,createFeature
FNF:1
FNH:0
FNDA:0,createFeature
DA:43,0
DA:44,0
DA:47,0
DA:48,0
DA:49,0
DA:50,0
DA:52,0
DA:56,0
DA:57,0
DA:58,0
DA:62,0
DA:67,0
DA:68,0
DA:71,0
DA:74,0
DA:75,0
DA:76,0
DA:79,0
DA:83,0
DA:84,0
DA:92,0
DA:93,0
DA:95,0
DA:100,0
DA:104,0
DA:107,0
DA:108,0
DA:109,0
DA:111,0
DA:114,0
DA:116,0
DA:130,0
DA:131,0
LF:33
LH:0
BRDA:49,0,0,0
BRDA:49,0,1,0
BRDA:56,1,0,0
BRDA:56,1,1,0
BRDA:57,2,0,0
BRDA:67,3,0,0
BRDA:67,4,0,0
BRDA:67,4,1,0
BRDA:75,5,0,0
BRDA:75,5,1,0
BRDA:83,6,0,0
BRDA:92,7,0,0
BRDA:92,7,1,0
BRDA:107,8,0,0
BRDA:107,8,1,0
BRF:15
BRH:0
end_of_record
TN:
SF:src/compositions/createTask.ts
FN:36,createTask
FNF:1
FNH:0
FNDA:0,createTask
DA:42,0
DA:43,0
DA:46,0
DA:47,0
DA:48,0
DA:49,0
DA:51,0
DA:55,0
DA:56,0
DA:57,0
DA:61,0
DA:66,0
DA:67,0
DA:70,0
DA:73,0
DA:74,0
DA:75,0
DA:78,0
DA:82,0
DA:83,0
DA:91,0
DA:92,0
DA:94,0
DA:99,0
DA:100,0
DA:103,0
DA:104,0
DA:105,0
DA:107,0
DA:110,0
DA:112,0
DA:126,0
DA:127,0
LF:33
LH:0
BRDA:48,0,0,0
BRDA:48,0,1,0
BRDA:55,1,0,0
BRDA:55,1,1,0
BRDA:56,2,0,0
BRDA:66,3,0,0
BRDA:66,4,0,0
BRDA:66,4,1,0
BRDA:74,5,0,0
BRDA:74,5,1,0
BRDA:82,6,0,0
BRDA:91,7,0,0
BRDA:91,7,1,0
BRDA:103,8,0,0
BRDA:103,8,1,0
BRF:15
BRH:0
end_of_record
TN:
SF:src/github/branches/createBranch.ts
FN:9,createBranch
FNF:1
FNH:0
FNDA:0,createBranch
DA:15,0
DA:16,0
DA:23,0
DA:32,0
LF:4
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/github/branches/createLinkedBranch.ts
FN:9,createLinkedBranch
FNF:1
FNH:0
FNDA:0,createLinkedBranch
DA:18,0
DA:20,0
DA:26,0
DA:29,0
DA:34,0
DA:35,0
DA:38,0
DA:44,0
DA:47,0
DA:68,0
DA:75,0
DA:77,0
DA:79,0
DA:80,0
DA:81,0
DA:82,0
DA:87,0
DA:88,0
DA:89,0
DA:99,0
DA:102,0
DA:105,0
DA:111,0
DA:122,0
DA:124,0
DA:125,0
DA:126,0
DA:131,0
DA:133,0
DA:134,0
DA:139,0
DA:140,0
DA:154,0
LF:33
LH:0
BRDA:77,0,0,0
BRDA:77,1,0,0
BRDA:77,1,1,0
BRDA:87,2,0,0
BRDA:87,2,1,0
BRDA:139,3,0,0
BRF:6
BRH:0
end_of_record
TN:
SF:src/github/branches/getBranch.ts
FN:9,getBranch
FNF:1
FNH:0
FNDA:0,getBranch
DA:15,0
DA:16,0
DA:22,0
DA:31,0
LF:4
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/github/comments/createComment.ts
FN:9,createComment
FNF:1
FNH:0
FNDA:0,createComment
DA:16,0
DA:17,0
DA:24,0
DA:34,0
LF:4
LH:0
BRDA:29,0,0,0
BRDA:29,0,1,0
BRF:2
BRH:0
end_of_record
TN:
SF:src/github/issues/addIssueLabel.ts
FN:9,addIssueLabel
FNF:1
FNH:1
FNDA:1,addIssueLabel
DA:16,1
DA:17,1
DA:24,0
DA:26,1
LF:4
LH:3
BRF:0
BRH:0
end_of_record
TN:
SF:src/github/issues/addSubIssue.ts
FN:9,addSubIssue
FNF:1
FNH:0
FNDA:0,addSubIssue
DA:16,0
DA:18,0
DA:24,0
DA:26,0
DA:36,0
DA:39,0
DA:46,0
DA:53,0
DA:56,0
LF:9
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/github/issues/createIssue.ts
FN:9,createIssue
FNF:1
FNH:1
FNDA:1,createIssue
DA:15,1
DA:16,1
DA:26,0
DA:38,1
LF:4
LH:3
BRDA:20,0,0,0
BRDA:20,0,1,0
BRDA:21,1,0,0
BRDA:21,1,1,0
BRDA:22,2,0,0
BRDA:22,2,1,0
BRF:6
BRH:0
end_of_record
TN:
SF:src/github/issues/createIssueType.ts
FN:30,createIssueType
FN:63,getOrganizationIssueTypes
FN:91,ensureIssueTypes
FN:103,(anonymous_3)
FNF:4
FNH:0
FNDA:0,createIssueType
FNDA:0,getOrganizationIssueTypes
FNDA:0,ensureIssueTypes
FNDA:0,(anonymous_3)
DA:35,0
DA:36,0
DA:48,0
DA:53,0
DA:67,0
DA:68,0
DA:76,0
DA:81,0
DA:95,0
DA:97,0
DA:99,0
DA:101,0
DA:103,0
DA:104,0
DA:109,0
DA:143,0
DA:144,0
DA:145,0
DA:146,0
DA:147,0
DA:148,0
DA:150,0
DA:155,0
DA:160,0
LF:24
LH:0
BRDA:39,0,0,0
BRDA:39,0,1,0
BRDA:40,1,0,0
BRDA:40,1,1,0
BRDA:41,2,0,0
BRDA:41,2,1,0
BRDA:101,3,0,0
BRDA:144,4,0,0
BRDA:146,5,0,0
BRDA:146,5,1,0
BRF:10
BRH:0
end_of_record
TN:
SF:src/github/issues/getIssue.ts
FN:9,getIssue
FNF:1
FNH:1
FNDA:2,getIssue
DA:15,2
DA:16,2
DA:22,0
DA:34,2
LF:4
LH:3
BRF:0
BRH:0
end_of_record
TN:
SF:src/github/issues/getIssueTypes.ts
FN:15,getIssueTypes
FN:48,(anonymous_1)
FNF:2
FNH:0
FNDA:0,getIssueTypes
FNDA:0,(anonymous_1)
DA:20,0
DA:21,0
DA:35,0
DA:44,0
DA:47,0
DA:48,0
DA:49,0
DA:52,0
DA:57,0
LF:9
LH:0
BRDA:44,0,0,0
BRDA:44,0,1,0
BRF:2
BRH:0
end_of_record
TN:
SF:src/github/issues/resolveIssueTypes.ts
FN:29,resolveIssueTypes
FN:55,(anonymous_1)
FN:90,hasIssueTypes
FN:104,getIssueTypeId
FN:119,clearIssueTypeCache
FNF:5
FNH:0
FNDA:0,resolveIssueTypes
FNDA:0,(anonymous_1)
FNDA:0,hasIssueTypes
FNDA:0,getIssueTypeId
FNDA:0,clearIssueTypeCache
DA:24,4
DA:34,0
DA:35,0
DA:38,0
DA:39,0
DA:46,0
DA:52,0
DA:55,0
DA:56,0
DA:57,0
DA:58,0
DA:59,0
DA:60,0
DA:61,0
DA:62,0
DA:63,0
DA:64,0
DA:65,0
DA:66,0
DA:71,0
DA:73,0
DA:75,0
DA:80,0
DA:95,0
DA:96,0
DA:98,0
DA:110,0
DA:111,0
DA:113,0
DA:120,0
LF:30
LH:1
BRDA:38,0,0,0
BRDA:57,1,0,0
BRDA:57,1,1,0
BRDA:59,2,0,0
BRDA:59,2,1,0
BRDA:61,3,0,0
BRDA:61,3,1,0
BRDA:63,4,0,0
BRDA:63,4,1,0
BRDA:65,5,0,0
BRDA:96,6,0,0
BRDA:111,7,0,0
BRF:12
BRH:0
end_of_record
TN:
SF:src/github/issues/setIssueType.ts
FN:10,setIssueType
FN:54,setIssueTypeByName
FNF:2
FNH:0
FNDA:0,setIssueType
FNDA:0,setIssueTypeByName
DA:15,0
DA:16,0
DA:33,0
DA:42,0
DA:44,0
DA:61,0
DA:63,0
DA:66,0
DA:67,0
DA:68,0
DA:74,0
DA:75,0
DA:76,0
DA:77,0
DA:81,0
DA:87,0
DA:90,0
DA:92,0
DA:93,0
DA:96,0
DA:98,0
LF:21
LH:0
BRDA:67,0,0,0
BRDA:75,1,0,0
BRDA:92,2,0,0
BRF:3
BRH:0
end_of_record
TN:
SF:src/github/projects/addIssueToProject.ts
FN:9,addIssueToProject
FNF:1
FNH:0
FNDA:0,addIssueToProject
DA:16,0
DA:18,0
DA:24,0
DA:27,0
DA:40,0
DA:45,0
DA:47,0
DA:52,0
LF:8
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/github/projects/addIssueToProjectV2.ts
FN:15,addIssueToProjectV2
FNF:1
FNH:0
FNDA:0,addIssueToProjectV2
DA:20,0
DA:21,0
DA:43,0
DA:49,0
DA:51,0
DA:60,0
LF:6
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/github/projects/assignAITeammate.ts
FN:30,assignAITeammate
FN:51,(anonymous_1)
FN:102,getAIAssignments
FN:120,(anonymous_3)
FN:173,(anonymous_4)
FN:206,getAllAIAssignments
FN:269,(anonymous_6)
FN:275,(anonymous_7)
FNF:8
FNH:0
FNDA:0,assignAITeammate
FNDA:0,(anonymous_1)
FNDA:0,getAIAssignments
FNDA:0,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,getAllAIAssignments
FNDA:0,(anonymous_6)
FNDA:0,(anonymous_7)
DA:36,0
DA:37,0
DA:40,0
DA:41,0
DA:42,0
DA:48,0
DA:51,0
DA:52,0
DA:53,0
DA:60,0
DA:77,0
DA:79,0
DA:81,0
DA:91,0
DA:92,0
DA:107,0
DA:108,0
DA:111,0
DA:112,0
DA:113,0
DA:119,0
DA:120,0
DA:121,0
DA:122,0
DA:129,0
DA:163,0
DA:164,0
DA:167,0
DA:169,0
DA:170,0
DA:173,0
DA:174,0
DA:177,0
DA:178,0
DA:188,0
DA:190,0
DA:195,0
DA:196,0
DA:210,0
DA:211,0
DA:214,0
DA:215,0
DA:216,0
DA:222,0
DA:225,0
DA:259,0
DA:260,0
DA:263,0
DA:265,0
DA:266,0
DA:269,0
DA:270,0
DA:274,0
DA:275,0
DA:276,0
DA:279,0
DA:288,0
DA:290,0
DA:295,0
DA:296,0
LF:60
LH:0
BRDA:41,0,0,0
BRDA:41,1,0,0
BRDA:41,1,1,0
BRDA:52,2,0,0
BRDA:112,3,0,0
BRDA:112,4,0,0
BRDA:112,4,1,0
BRDA:121,5,0,0
BRDA:170,6,0,0
BRDA:174,7,0,0
BRDA:174,7,1,0
BRDA:177,8,0,0
BRDA:215,9,0,0
BRDA:215,10,0,0
BRDA:215,10,1,0
BRDA:266,11,0,0
BRDA:274,12,0,0
BRF:17
BRH:0
end_of_record
TN:
SF:src/github/projects/createProject.ts
FN:9,createProject
FNF:1
FNH:0
FNDA:0,createProject
DA:14,0
DA:16,0
DA:34,0
DA:35,0
DA:37,0
DA:42,0
DA:44,0
DA:55,0
LF:8
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/github/projects/createProjectField.ts
FN:36,createAITeammateField
FN:105,createProjectField
FN:167,getProjectFields
FN:209,(anonymous_3)
FN:228,findAITeammateField
FN:241,(anonymous_5)
FNF:6
FNH:0
FNDA:0,createAITeammateField
FNDA:0,createProjectField
FNDA:0,getProjectFields
FNDA:0,(anonymous_3)
FNDA:0,findAITeammateField
FNDA:0,(anonymous_5)
DA:40,0
DA:41,0
DA:44,0
DA:78,0
DA:79,0
DA:81,0
DA:83,0
DA:94,0
DA:95,0
DA:109,0
DA:110,0
DA:113,0
DA:141,0
DA:142,0
DA:144,0
DA:146,0
DA:156,0
DA:157,0
DA:171,0
DA:172,0
DA:174,0
DA:202,0
DA:203,0
DA:205,0
DA:207,0
DA:209,0
DA:217,0
DA:218,0
DA:232,0
DA:233,0
DA:234,0
DA:235,0
DA:241,0
DA:243,0
DA:248,0
LF:35
LH:0
BRDA:89,0,0,0
BRDA:89,0,1,0
BRDA:152,1,0,0
BRDA:152,1,1,0
BRDA:213,2,0,0
BRDA:213,2,1,0
BRDA:234,3,0,0
BRDA:234,4,0,0
BRDA:234,4,1,0
BRDA:237,5,0,0
BRDA:237,5,1,0
BRDA:245,6,0,0
BRDA:245,6,1,0
BRF:13
BRH:0
end_of_record
TN:
SF:src/github/projects/createProjectV2.ts
FN:22,createProjectV2
FNF:1
FNH:0
FNDA:0,createProjectV2
DA:27,0
DA:29,0
DA:38,0
DA:43,0
DA:46,0
DA:62,0
DA:68,0
DA:70,0
DA:80,0
LF:9
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/github/repositories/createOrgRepository.ts
FN:9,createOrgRepository
FNF:1
FNH:0
FNDA:0,createOrgRepository
DA:14,0
DA:15,0
DA:24,0
DA:38,0
LF:4
LH:0
BRDA:18,0,0,0
BRDA:18,0,1,0
BRDA:19,1,0,0
BRDA:19,1,1,0
BRDA:20,2,0,0
BRDA:20,2,1,0
BRDA:21,3,0,0
BRDA:21,3,1,0
BRF:8
BRH:0
end_of_record
TN:
SF:src/github/repositories/createRepository.ts
FN:9,createRepository
FNF:1
FNH:0
FNDA:0,createRepository
DA:13,0
DA:14,0
DA:22,0
DA:36,0
LF:4
LH:0
BRDA:16,0,0,0
BRDA:16,0,1,0
BRDA:17,1,0,0
BRDA:17,1,1,0
BRDA:18,2,0,0
BRDA:18,2,1,0
BRDA:19,3,0,0
BRDA:19,3,1,0
BRF:8
BRH:0
end_of_record
TN:
SF:src/github/repositories/getRepository.ts
FN:9,getRepository
FNF:1
FNH:0
FNDA:0,getRepository
DA:14,0
DA:15,0
DA:20,0
DA:34,0
LF:4
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/github/repositories/listRepositories.ts
FN:9,listRepositories
FN:17,(anonymous_1)
FNF:2
FNH:0
FNDA:0,listRepositories
FNDA:0,(anonymous_1)
DA:12,0
DA:13,0
DA:17,0
DA:28,0
DA:33,0
LF:5
LH:0
BRF:0
BRH:0
end_of_record
TN:
SF:src/github/utils/generateBranchName.ts
FN:6,generateBranchName
FN:7,(anonymous_1)
FNF:2
FNH:2
FNDA:4,generateBranchName
FNDA:4,(anonymous_1)
DA:7,4
DA:10,4
DA:12,4
DA:14,1
DA:16,2
DA:18,1
DA:20,0
LF:7
LH:6
BRDA:12,0,0,1
BRDA:12,0,1,2
BRDA:12,0,2,1
BRDA:12,0,3,0
BRDA:14,1,0,1
BRDA:14,1,1,1
BRDA:16,2,0,2
BRDA:16,2,1,1
BRF:8
BRH:7
end_of_record
