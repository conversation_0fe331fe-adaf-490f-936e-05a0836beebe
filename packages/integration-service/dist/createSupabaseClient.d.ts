/**
 * Create Supabase client
 * Single responsibility: Supabase client creation and tenant queries
 */
import { type SupabaseClient } from '@supabase/supabase-js';
import type { SupabaseCredentials, TenantData, IntegrationResult } from './types';
/**
 * Create Supabase client
 */
export declare function createSupabaseClient(credentials: SupabaseCredentials): SupabaseClient;
/**
 * Get installation ID by organization name
 */
export declare function getInstallationIdByOrg(client: SupabaseClient, organizationName: string): Promise<IntegrationResult<TenantData>>;
/**
 * List all organizations
 */
export declare function listOrganizations(client: SupabaseClient): Promise<IntegrationResult<TenantData[]>>;
//# sourceMappingURL=createSupabaseClient.d.ts.map