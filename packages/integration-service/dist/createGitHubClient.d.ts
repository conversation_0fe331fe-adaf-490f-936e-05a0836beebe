/**
 * Create GitHub client
 * Single responsibility: GitHub client creation with App authentication
 */
import { Octokit } from '@octokit/rest';
import type { GitHubCredentials, IntegrationResult } from './types';
/**
 * Create GitHub client with App authentication
 */
export declare function createGitHubClient(credentials: GitHubCredentials): Octokit;
/**
 * Create GitHub client with installation token
 */
export declare function createGitHubInstallationClient(credentials: GitHubCredentials, installationId: number): Promise<IntegrationResult<Octokit>>;
//# sourceMappingURL=createGitHubClient.d.ts.map