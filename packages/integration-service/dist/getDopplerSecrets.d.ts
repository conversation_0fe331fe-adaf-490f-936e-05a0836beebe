/**
 * Get secrets from <PERSON><PERSON><PERSON>
 * Single responsibility: Doppler secret retrieval
 */
import type { DopplerConfig, IntegrationResult, SupabaseCredentials, GitHubCredentials } from './types';
/**
 * Get Supabase credentials from Doppler
 */
export declare function getSupabaseCredentials(config: DopplerConfig): Promise<IntegrationResult<SupabaseCredentials>>;
/**
 * Get GitHub credentials from Doppler
 */
export declare function getGitHubCredentials(config: DopplerConfig): Promise<IntegrationResult<GitHubCredentials>>;
//# sourceMappingURL=getDopplerSecrets.d.ts.map