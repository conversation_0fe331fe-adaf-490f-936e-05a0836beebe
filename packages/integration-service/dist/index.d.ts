/**
 * Integration Service - Functional Client Factory
 * Lightweight service for creating authenticated clients
 */
export type { DopplerConfig, SupabaseCredentials, GitHubCredentials, TenantData, IntegrationResult } from './types.js';
export { getSupabaseCredentials, getGitHubCredentials } from './getDopplerSecrets.js';
export { createSupabaseClient, getInstallationIdByOrg, listOrganizations } from './createSupabaseClient.js';
export { createGitHubClient, createGitHubInstallationClient } from './createGitHubClient.js';
/**
 * Convenience function: Get installation ID for organization
 */
export declare function getInstallationId(dopplerToken: string, organizationName: string): Promise<import("./types.js").IntegrationResult<import("./types.js").TenantData> | import("./types.js").IntegrationResult<import("./types.js").SupabaseCredentials>>;
/**
 * Convenience function: Create complete GitHub setup for organization
 */
export declare function createGitHubSetup(dopplerToken: string, organizationName: string): Promise<any>;
//# sourceMappingURL=index.d.ts.map