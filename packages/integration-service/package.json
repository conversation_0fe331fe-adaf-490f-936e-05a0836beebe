{"name": "@brainstack/integration-service", "version": "1.0.0", "description": "Lightweight functional client factory for Doppler, Supabase, and GitHub integrations", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "NODE_OPTIONS='--experimental-vm-modules' jest", "test:watch": "NODE_OPTIONS='--experimental-vm-modules' jest --watch", "test:coverage": "NODE_OPTIONS='--experimental-vm-modules' jest --coverage"}, "dependencies": {"@dopplerhq/node-sdk": "^1.3.0", "@supabase/supabase-js": "^2.50.0", "@octokit/rest": "^20.0.0", "jsonwebtoken": "^9.0.0"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.0", "@types/node": "^20.0.0", "dotenv": "^16.3.1", "jest": "^29.7.0", "ts-jest": "^29.1.0", "typescript": "^5.0.0"}, "keywords": ["integration", "doppler", "supabase", "github", "client-factory", "functional"], "author": "Brainstack", "license": "MIT"}