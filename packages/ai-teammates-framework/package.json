{"name": "@brainstack/ai-teammates-framework", "version": "0.1.0", "description": "Generic, reusable AI teammates framework with plugin architecture", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./core": {"import": "./dist/core/index.js", "types": "./dist/core/index.d.ts"}, "./plugins": {"import": "./dist/plugins/index.js", "types": "./dist/plugins/index.d.ts"}, "./shared": {"import": "./dist/shared/index.js", "types": "./dist/shared/index.d.ts"}}, "scripts": {"build": "tsc", "build:watch": "tsc --watch", "test": "NODE_OPTIONS='--experimental-vm-modules' jest", "test:watch": "NODE_OPTIONS='--experimental-vm-modules' jest --watch", "test:coverage": "NODE_OPTIONS='--experimental-vm-modules' jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rm -rf dist", "dev": "npm run build:watch", "prepublishOnly": "npm run clean && npm run build"}, "keywords": ["ai", "teammates", "framework", "mcp", "plugin", "agents"], "author": "Brainstack", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.6.0"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint": "^8.57.0", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.3.3"}, "files": ["dist", "README.md"], "repository": {"type": "git", "url": "https://github.com/mouimet-infinisoft/AISDLC.git", "directory": "packages/ai-teammates-framework"}}