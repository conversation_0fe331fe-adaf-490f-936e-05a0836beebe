
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">74.57% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>349/468</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">84.58% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>192/227</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">85.61% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>125/146</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">74.5% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>339/455</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="core"><a href="core/index.html">core</a></td>
	<td data-value="30.99" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 30%"></div><div class="cover-empty" style="width: 70%"></div></div>
	</td>
	<td data-value="30.99" class="pct low">30.99%</td>
	<td data-value="171" class="abs low">53/171</td>
	<td data-value="78.18" class="pct medium">78.18%</td>
	<td data-value="110" class="abs medium">86/110</td>
	<td data-value="41.66" class="pct low">41.66%</td>
	<td data-value="36" class="abs low">15/36</td>
	<td data-value="31.13" class="pct low">31.13%</td>
	<td data-value="167" class="abs low">52/167</td>
	</tr>

<tr>
	<td class="file high" data-value="core/services"><a href="core/services/index.html">core/services</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="168" class="abs high">168/168</td>
	<td data-value="91.8" class="pct medium">91.8%</td>
	<td data-value="61" class="abs medium">56/61</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="57" class="abs high">57/57</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="166" class="abs high">166/166</td>
	</tr>

<tr>
	<td class="file high" data-value="plugins"><a href="plugins/index.html">plugins</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="31" class="abs high">31/31</td>
	<td data-value="90" class="pct medium">90%</td>
	<td data-value="10" class="abs medium">9/10</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="31" class="abs high">31/31</td>
	</tr>

<tr>
	<td class="file medium" data-value="shared"><a href="shared/index.html">shared</a></td>
	<td data-value="98.97" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 98%"></div><div class="cover-empty" style="width: 2%"></div></div>
	</td>
	<td data-value="98.97" class="pct medium">98.97%</td>
	<td data-value="98" class="abs medium">97/98</td>
	<td data-value="89.13" class="pct medium">89.13%</td>
	<td data-value="46" class="abs medium">41/46</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="44" class="abs high">44/44</td>
	<td data-value="98.9" class="pct medium">98.9%</td>
	<td data-value="91" class="abs medium">90/91</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-01T08:43:31.393Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    