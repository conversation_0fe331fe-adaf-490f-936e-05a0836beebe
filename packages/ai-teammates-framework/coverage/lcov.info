TN:
SF:src/functional/builtin/conversation-prompt.ts
FN:24,(anonymous_0)
FN:37,(anonymous_1)
FNF:2
FNH:2
FNDA:3,(anonymous_0)
FNDA:4,(anonymous_1)
DA:11,1
DA:24,1
DA:27,3
DA:28,3
DA:30,3
DA:34,3
DA:37,1
DA:38,4
LF:8
LH:8
BRDA:28,0,0,3
BRDA:28,0,1,2
BRF:2
BRH:2
end_of_record
TN:
SF:src/functional/builtin/memory-resource.ts
FN:10,(anonymous_0)
FN:15,(anonymous_1)
FNF:2
FNH:2
FNDA:3,(anonymous_0)
FNDA:4,(anonymous_1)
DA:10,1
DA:11,3
DA:12,3
DA:15,1
DA:16,4
LF:5
LH:5
BRDA:11,0,0,3
BRDA:11,0,1,1
BRF:2
BRH:2
end_of_record
TN:
SF:src/functional/builtin/memory-tool.ts
FN:35,(anonymous_0)
FN:73,(anonymous_1)
FNF:2
FNH:2
FNDA:10,(anonymous_0)
FNDA:11,(anonymous_1)
DA:11,1
DA:35,1
DA:38,10
DA:39,10
DA:40,10
DA:41,10
DA:43,10
DA:45,3
DA:46,1
DA:48,2
DA:51,2
DA:52,1
DA:54,1
DA:57,2
DA:58,1
DA:60,1
DA:63,1
DA:66,1
DA:69,1
DA:73,1
DA:74,11
LF:21
LH:21
BRDA:43,0,0,3
BRDA:43,0,1,2
BRDA:43,0,2,2
BRDA:43,0,3,1
BRDA:43,0,4,1
BRDA:43,0,5,1
BRDA:45,1,0,1
BRDA:45,2,0,3
BRDA:45,2,1,2
BRDA:48,3,0,1
BRDA:48,3,1,1
BRDA:51,4,0,1
BRDA:57,5,0,1
BRF:13
BRH:13
end_of_record
TN:
SF:src/functional/config/parse-env.ts
FN:9,(anonymous_0)
FN:77,(anonymous_1)
FN:78,(anonymous_2)
FN:80,(anonymous_3)
FNF:4
FNH:4
FNDA:6,(anonymous_0)
FNDA:25,(anonymous_1)
FNDA:70,(anonymous_2)
FNDA:5,(anonymous_3)
DA:9,1
DA:10,6
DA:11,6
DA:71,5
DA:73,1
DA:77,1
DA:78,70
DA:80,1
DA:81,5
DA:82,3
DA:85,2
DA:86,2
DA:88,1
LF:13
LH:13
BRDA:13,0,0,6
BRDA:13,0,1,4
BRDA:14,1,0,5
BRDA:14,1,1,4
BRDA:16,2,0,5
BRDA:16,2,1,5
BRDA:17,3,0,5
BRDA:17,3,1,5
BRDA:18,4,0,5
BRDA:18,4,1,4
BRDA:19,5,0,5
BRDA:19,5,1,4
BRDA:20,6,0,5
BRDA:20,6,1,5
BRDA:24,7,0,5
BRDA:24,7,1,5
BRDA:25,8,0,5
BRDA:25,8,1,5
BRDA:26,9,0,5
BRDA:26,9,1,5
BRDA:27,10,0,5
BRDA:27,10,1,5
BRDA:31,11,0,5
BRDA:31,11,1,5
BRDA:32,12,0,5
BRDA:32,12,1,5
BRDA:33,13,0,5
BRDA:33,13,1,5
BRDA:36,14,0,5
BRDA:36,14,1,5
BRDA:37,15,0,5
BRDA:37,15,1,5
BRDA:39,16,0,5
BRDA:39,16,1,5
BRDA:39,16,2,5
BRDA:40,17,0,5
BRDA:40,17,1,5
BRDA:40,17,2,5
BRDA:41,18,0,5
BRDA:41,18,1,5
BRDA:41,18,2,5
BRDA:45,19,0,5
BRDA:45,19,1,5
BRDA:46,20,0,5
BRDA:46,20,1,5
BRDA:46,20,2,5
BRDA:47,21,0,5
BRDA:47,21,1,5
BRDA:47,21,2,4
BRDA:51,22,0,5
BRDA:51,22,1,5
BRDA:56,23,0,5
BRDA:56,23,1,5
BRDA:56,23,2,4
BRDA:57,24,0,5
BRDA:57,24,1,5
BRDA:58,25,0,5
BRDA:58,25,1,5
BRDA:61,26,0,5
BRDA:61,26,1,5
BRDA:61,26,2,4
BRDA:62,27,0,5
BRDA:62,27,1,4
BRDA:65,28,0,5
BRDA:65,28,1,5
BRDA:66,29,0,5
BRDA:66,29,1,5
BRDA:81,30,0,3
BRF:68
BRH:68
end_of_record
TN:
SF:src/functional/config/validate.ts
FN:8,(anonymous_0)
FNF:1
FNH:1
FNDA:4,(anonymous_0)
DA:8,1
DA:9,4
DA:12,4
DA:13,1
DA:21,4
DA:22,1
DA:30,4
DA:31,1
DA:38,4
LF:9
LH:9
BRDA:12,0,0,1
BRDA:21,1,0,1
BRDA:30,2,0,1
BRDA:38,3,0,1
BRDA:38,3,1,3
BRF:5
BRH:5
end_of_record
TN:
SF:src/functional/framework/create-framework.ts
FN:24,(anonymous_0)
FNF:1
FNH:1
FNDA:3,(anonymous_0)
DA:24,1
DA:29,3
LF:2
LH:2
BRDA:26,0,0,2
BRDA:27,1,0,2
BRDA:28,2,0,2
BRF:3
BRH:3
end_of_record
TN:
SF:src/functional/logging/create-entry.ts
FN:7,(anonymous_0)
FNF:1
FNH:1
FNDA:18,(anonymous_0)
DA:7,4
DA:12,18
LF:2
LH:2
BRDA:11,0,0,7
BRF:1
BRH:1
end_of_record
TN:
SF:src/functional/logging/format-entry.ts
FN:7,(anonymous_0)
FNF:1
FNH:1
FNDA:11,(anonymous_0)
DA:7,4
DA:8,11
DA:9,11
DA:10,11
DA:14,11
LF:5
LH:5
BRDA:10,0,0,1
BRDA:10,0,1,10
BRF:2
BRH:2
end_of_record
TN:
SF:src/functional/logging/output-entry.ts
FN:8,(anonymous_0)
FNF:1
FNH:1
FNDA:6,(anonymous_0)
DA:8,3
DA:9,6
DA:11,6
DA:14,4
DA:15,4
DA:17,1
DA:18,1
DA:20,1
DA:21,1
LF:9
LH:9
BRDA:11,0,0,1
BRDA:11,0,1,4
BRDA:11,0,2,1
BRDA:11,0,3,1
BRF:4
BRH:4
end_of_record
TN:
SF:src/functional/mcp/create-response.ts
FN:7,(anonymous_0)
FNF:1
FNH:1
FNDA:16,(anonymous_0)
DA:7,3
DA:10,16
LF:2
LH:2
BRDA:9,0,0,4
BRF:1
BRH:1
end_of_record
TN:
SF:src/functional/mcp/handle-prompt-get.ts
FN:10,(anonymous_0)
FN:21,(anonymous_1)
FNF:2
FNH:2
FNDA:6,(anonymous_0)
FNDA:4,(anonymous_1)
DA:10,1
DA:14,6
DA:15,6
DA:17,6
DA:18,1
DA:21,5
DA:22,5
DA:23,1
DA:26,4
DA:27,4
DA:29,3
DA:30,2
DA:32,1
DA:35,1
LF:14
LH:14
BRDA:15,0,0,6
BRDA:15,0,1,1
BRDA:17,1,0,1
BRDA:22,2,0,1
BRDA:29,3,0,2
BRDA:29,3,1,1
BRF:6
BRH:6
end_of_record
TN:
SF:src/functional/mcp/handle-resource-read.ts
FN:10,(anonymous_0)
FN:20,(anonymous_1)
FNF:2
FNH:2
FNDA:5,(anonymous_0)
FNDA:3,(anonymous_1)
DA:10,1
DA:14,5
DA:16,5
DA:17,1
DA:20,4
DA:21,4
DA:22,1
DA:25,3
DA:26,3
DA:28,2
DA:29,1
DA:31,1
DA:34,1
LF:13
LH:13
BRDA:16,0,0,1
BRDA:21,1,0,1
BRDA:28,2,0,1
BRDA:28,2,1,1
BRF:4
BRH:4
end_of_record
TN:
SF:src/functional/mcp/handle-tool-call.ts
FN:10,(anonymous_0)
FN:21,(anonymous_1)
FNF:2
FNH:2
FNDA:5,(anonymous_0)
FNDA:3,(anonymous_1)
DA:10,1
DA:14,5
DA:15,5
DA:17,5
DA:18,1
DA:21,4
DA:22,4
DA:23,1
DA:26,3
DA:27,3
DA:29,2
DA:30,1
DA:32,1
DA:35,1
LF:14
LH:14
BRDA:15,0,0,5
BRDA:15,0,1,1
BRDA:17,1,0,1
BRDA:22,2,0,1
BRDA:29,3,0,1
BRDA:29,3,1,1
BRF:6
BRH:6
end_of_record
TN:
SF:src/functional/memory/create-entry.ts
FN:7,(anonymous_0)
FNF:1
FNH:1
FNDA:11,(anonymous_0)
DA:7,2
DA:11,11
LF:2
LH:2
BRF:0
BRH:0
end_of_record
TN:
SF:src/functional/memory/filter-valid.ts
FN:8,(anonymous_0)
FN:11,(anonymous_1)
FNF:2
FNH:2
FNDA:8,(anonymous_0)
FNDA:10,(anonymous_1)
DA:8,2
DA:11,10
LF:2
LH:2
BRF:0
BRH:0
end_of_record
TN:
SF:src/functional/memory/is-expired.ts
FN:7,(anonymous_0)
FNF:1
FNH:1
FNDA:10,(anonymous_0)
DA:7,2
DA:8,10
DA:9,9
DA:12,1
DA:13,1
DA:14,1
DA:16,1
LF:7
LH:7
BRDA:8,0,0,9
BRF:1
BRH:1
end_of_record
TN:
SF:src/functional/services/logger.ts
FN:17,(anonymous_0)
FN:21,(anonymous_1)
FN:28,(anonymous_2)
FN:30,(anonymous_3)
FNF:4
FNH:4
FNDA:9,(anonymous_0)
FNDA:9,(anonymous_1)
FNDA:36,(anonymous_2)
FNDA:9,(anonymous_3)
DA:17,2
DA:21,9
DA:22,9
DA:23,2
DA:25,9
DA:28,9
DA:30,36
DA:31,9
DA:35,9
LF:9
LH:9
BRDA:19,0,0,4
BRDA:22,1,0,2
BRDA:30,2,0,8
BRF:3
BRH:3
end_of_record
TN:
SF:src/functional/services/memory.ts
FN:19,(anonymous_0)
FN:20,(anonymous_1)
FN:23,(anonymous_2)
FN:25,(anonymous_3)
FN:29,(anonymous_4)
FN:31,(anonymous_5)
FN:34,(anonymous_6)
FN:35,(anonymous_7)
FN:37,(anonymous_8)
FN:39,(anonymous_9)
FN:42,(anonymous_10)
FNF:11
FNH:11
FNDA:14,(anonymous_0)
FNDA:11,(anonymous_1)
FNDA:3,(anonymous_2)
FNDA:1,(anonymous_3)
FNDA:2,(anonymous_4)
FNDA:2,(anonymous_5)
FNDA:1,(anonymous_6)
FNDA:2,(anonymous_7)
FNDA:1,(anonymous_8)
FNDA:1,(anonymous_9)
FNDA:1,(anonymous_10)
DA:19,14
DA:21,11
DA:24,3
DA:25,3
DA:26,3
DA:30,2
DA:31,2
DA:35,2
DA:37,1
DA:40,1
DA:43,1
LF:11
LH:11
BRF:0
BRH:0
end_of_record
TN:
SF:src/functional/services/uri-builder.ts
FN:17,(anonymous_0)
FN:18,(anonymous_1)
FN:19,(anonymous_2)
FN:20,(anonymous_3)
FN:21,(anonymous_4)
FNF:5
FNH:5
FNDA:9,(anonymous_0)
FNDA:3,(anonymous_1)
FNDA:1,(anonymous_2)
FNDA:1,(anonymous_3)
FNDA:1,(anonymous_4)
DA:17,9
DA:18,3
DA:19,1
DA:20,1
DA:21,1
LF:5
LH:5
BRF:0
BRH:0
end_of_record
TN:
SF:src/functional/tools/create-prompt.ts
FN:7,(anonymous_0)
FNF:1
FNH:1
FNDA:12,(anonymous_0)
DA:7,4
DA:12,12
LF:2
LH:2
BRF:0
BRH:0
end_of_record
TN:
SF:src/functional/tools/create-resource.ts
FN:7,(anonymous_0)
FNF:1
FNH:1
FNDA:10,(anonymous_0)
DA:7,4
DA:13,10
LF:2
LH:2
BRF:0
BRH:0
end_of_record
TN:
SF:src/functional/tools/create-tool.ts
FN:7,(anonymous_0)
FNF:1
FNH:1
FNDA:17,(anonymous_0)
DA:7,4
DA:12,17
LF:2
LH:2
BRF:0
BRH:0
end_of_record
TN:
SF:src/functional/uri/build-conversation.ts
FN:5,(anonymous_0)
FNF:1
FNH:1
FNDA:1,(anonymous_0)
DA:5,2
DA:6,1
LF:2
LH:2
BRF:0
BRH:0
end_of_record
TN:
SF:src/functional/uri/build-memory.ts
FN:5,(anonymous_0)
FNF:1
FNH:1
FNDA:3,(anonymous_0)
DA:5,2
DA:6,3
LF:2
LH:2
BRF:0
BRH:0
end_of_record
TN:
SF:src/functional/uri/build-project.ts
FN:5,(anonymous_0)
FNF:1
FNH:1
FNDA:1,(anonymous_0)
DA:5,2
DA:6,1
LF:2
LH:2
BRF:0
BRH:0
end_of_record
TN:
SF:src/functional/uri/build-template.ts
FN:5,(anonymous_0)
FNF:1
FNH:1
FNDA:1,(anonymous_0)
DA:5,2
DA:6,1
LF:2
LH:2
BRF:0
BRH:0
end_of_record
TN:
SF:src/functional/utils/compose.ts
FN:5,(anonymous_0)
FN:6,(anonymous_1)
FN:7,(anonymous_2)
FN:10,(anonymous_3)
FN:13,(anonymous_4)
FN:15,(anonymous_5)
FN:17,(anonymous_6)
FN:17,(anonymous_7)
FN:19,(anonymous_8)
FN:22,(anonymous_9)
FN:24,(anonymous_10)
FN:26,(anonymous_11)
FN:26,(anonymous_12)
FN:28,(anonymous_13)
FN:28,(anonymous_14)
FN:33,(anonymous_15)
FN:36,(anonymous_16)
FNF:17
FNH:17
FNDA:21,(anonymous_0)
FNDA:11,(anonymous_1)
FNDA:10,(anonymous_2)
FNDA:1,(anonymous_3)
FNDA:1,(anonymous_4)
FNDA:1,(anonymous_5)
FNDA:1,(anonymous_6)
FNDA:1,(anonymous_7)
FNDA:1,(anonymous_8)
FNDA:1,(anonymous_9)
FNDA:2,(anonymous_10)
FNDA:1,(anonymous_11)
FNDA:1,(anonymous_12)
FNDA:1,(anonymous_13)
FNDA:1,(anonymous_14)
FNDA:1,(anonymous_15)
FNDA:2,(anonymous_16)
DA:5,21
DA:6,11
DA:7,10
DA:10,3
DA:13,1
DA:15,3
DA:17,1
DA:19,3
DA:22,1
DA:24,3
DA:26,3
DA:28,3
DA:29,1
DA:30,1
DA:33,3
DA:36,2
LF:16
LH:16
BRDA:36,0,0,1
BRDA:36,0,1,1
BRF:2
BRH:2
end_of_record
TN:
SF:src/functional/utils/result.ts
FN:7,(anonymous_0)
FN:12,(anonymous_1)
FN:17,(anonymous_2)
FN:20,(anonymous_3)
FN:23,(anonymous_4)
FN:29,(anonymous_5)
FN:35,(anonymous_6)
FNF:7
FNH:7
FNDA:33,(anonymous_0)
FNDA:20,(anonymous_1)
FNDA:48,(anonymous_2)
FNDA:14,(anonymous_3)
FNDA:2,(anonymous_4)
FNDA:3,(anonymous_5)
FNDA:2,(anonymous_6)
DA:7,33
DA:12,20
DA:17,12
DA:18,48
DA:20,12
DA:21,14
DA:23,12
DA:27,2
DA:29,12
DA:33,3
DA:35,12
DA:39,2
LF:12
LH:12
BRDA:27,0,0,1
BRDA:27,0,1,1
BRDA:33,1,0,2
BRDA:33,1,1,1
BRDA:39,2,0,1
BRDA:39,2,1,1
BRF:6
BRH:6
end_of_record
