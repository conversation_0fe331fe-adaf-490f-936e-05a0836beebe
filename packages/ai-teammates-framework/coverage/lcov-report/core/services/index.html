
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for core/services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> core/services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">86.3% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>145/168</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">77.04% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>47/61</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">80.7% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>46/57</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">86.74% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>144/166</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="logger.ts"><a href="logger.ts.html">logger.ts</a></td>
	<td data-value="65.57" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 65%"></div><div class="cover-empty" style="width: 35%"></div></div>
	</td>
	<td data-value="65.57" class="pct medium">65.57%</td>
	<td data-value="61" class="abs medium">40/61</td>
	<td data-value="63.63" class="pct medium">63.63%</td>
	<td data-value="22" class="abs medium">14/22</td>
	<td data-value="45" class="pct low">45%</td>
	<td data-value="20" class="abs low">9/20</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="60" class="abs medium">40/60</td>
	</tr>

<tr>
	<td class="file medium" data-value="memory-service.ts"><a href="memory-service.ts.html">memory-service.ts</a></td>
	<td data-value="96.77" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 96%"></div><div class="cover-empty" style="width: 4%"></div></div>
	</td>
	<td data-value="96.77" class="pct medium">96.77%</td>
	<td data-value="62" class="abs medium">60/62</td>
	<td data-value="83.33" class="pct medium">83.33%</td>
	<td data-value="18" class="abs medium">15/18</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="24" class="abs high">24/24</td>
	<td data-value="96.72" class="pct medium">96.72%</td>
	<td data-value="61" class="abs medium">59/61</td>
	</tr>

<tr>
	<td class="file high" data-value="uri-builder.ts"><a href="uri-builder.ts.html">uri-builder.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="45" class="abs high">45/45</td>
	<td data-value="85.71" class="pct medium">85.71%</td>
	<td data-value="21" class="abs medium">18/21</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="13" class="abs high">13/13</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="45" class="abs high">45/45</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-01T08:06:42.240Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    