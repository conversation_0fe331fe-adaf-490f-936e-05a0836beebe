
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for core</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> core</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">44.44% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>76/171</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">79.09% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>87/110</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">58.33% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>21/36</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">44.91% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>75/167</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="config-system.ts"><a href="config-system.ts.html">config-system.ts</a></td>
	<td data-value="90.56" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 90%"></div><div class="cover-empty" style="width: 10%"></div></div>
	</td>
	<td data-value="90.56" class="pct medium">90.56%</td>
	<td data-value="53" class="abs medium">48/53</td>
	<td data-value="94.31" class="pct medium">94.31%</td>
	<td data-value="88" class="abs medium">83/88</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="15" class="abs high">15/15</td>
	<td data-value="90.38" class="pct medium">90.38%</td>
	<td data-value="52" class="abs medium">47/52</td>
	</tr>

<tr>
	<td class="file low" data-value="teammate-engine.ts"><a href="teammate-engine.ts.html">teammate-engine.ts</a></td>
	<td data-value="23.72" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 23%"></div><div class="cover-empty" style="width: 77%"></div></div>
	</td>
	<td data-value="23.72" class="pct low">23.72%</td>
	<td data-value="118" class="abs low">28/118</td>
	<td data-value="18.18" class="pct low">18.18%</td>
	<td data-value="22" class="abs low">4/22</td>
	<td data-value="28.57" class="pct low">28.57%</td>
	<td data-value="21" class="abs low">6/21</td>
	<td data-value="24.34" class="pct low">24.34%</td>
	<td data-value="115" class="abs low">28/115</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-01T08:06:42.240Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    