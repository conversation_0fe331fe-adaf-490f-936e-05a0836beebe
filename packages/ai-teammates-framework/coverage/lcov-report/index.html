
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">67.94% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>318/468</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.21% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>173/227</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.71% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>112/146</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">68.57% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>312/455</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="core"><a href="core/index.html">core</a></td>
	<td data-value="44.44" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 44%"></div><div class="cover-empty" style="width: 56%"></div></div>
	</td>
	<td data-value="44.44" class="pct low">44.44%</td>
	<td data-value="171" class="abs low">76/171</td>
	<td data-value="79.09" class="pct medium">79.09%</td>
	<td data-value="110" class="abs medium">87/110</td>
	<td data-value="58.33" class="pct medium">58.33%</td>
	<td data-value="36" class="abs medium">21/36</td>
	<td data-value="44.91" class="pct low">44.91%</td>
	<td data-value="167" class="abs low">75/167</td>
	</tr>

<tr>
	<td class="file medium" data-value="core/services"><a href="core/services/index.html">core/services</a></td>
	<td data-value="86.3" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 86%"></div><div class="cover-empty" style="width: 14%"></div></div>
	</td>
	<td data-value="86.3" class="pct medium">86.3%</td>
	<td data-value="168" class="abs medium">145/168</td>
	<td data-value="77.04" class="pct medium">77.04%</td>
	<td data-value="61" class="abs medium">47/61</td>
	<td data-value="80.7" class="pct medium">80.7%</td>
	<td data-value="57" class="abs medium">46/57</td>
	<td data-value="86.74" class="pct medium">86.74%</td>
	<td data-value="166" class="abs medium">144/166</td>
	</tr>

<tr>
	<td class="file medium" data-value="plugins"><a href="plugins/index.html">plugins</a></td>
	<td data-value="64.51" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 64%"></div><div class="cover-empty" style="width: 36%"></div></div>
	</td>
	<td data-value="64.51" class="pct medium">64.51%</td>
	<td data-value="31" class="abs medium">20/31</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="10" class="abs medium">5/10</td>
	<td data-value="88.88" class="pct medium">88.88%</td>
	<td data-value="9" class="abs medium">8/9</td>
	<td data-value="64.51" class="pct medium">64.51%</td>
	<td data-value="31" class="abs medium">20/31</td>
	</tr>

<tr>
	<td class="file medium" data-value="shared"><a href="shared/index.html">shared</a></td>
	<td data-value="78.57" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 78%"></div><div class="cover-empty" style="width: 22%"></div></div>
	</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="98" class="abs medium">77/98</td>
	<td data-value="73.91" class="pct medium">73.91%</td>
	<td data-value="46" class="abs medium">34/46</td>
	<td data-value="84.09" class="pct medium">84.09%</td>
	<td data-value="44" class="abs medium">37/44</td>
	<td data-value="80.21" class="pct medium">80.21%</td>
	<td data-value="91" class="abs medium">73/91</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-01T08:06:42.240Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    