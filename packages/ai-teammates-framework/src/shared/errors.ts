/**
 * Framework Error Classes
 * Structured error handling for the AI Teammates Framework
 */

import type { FrameworkError } from './types.js';

export class BaseFrameworkError extends Error implements FrameworkError {
  public readonly code: string;
  public readonly context?: Record<string, unknown>;
  public readonly retryable: boolean;

  constructor(
    code: string,
    message: string,
    retryable = false,
    context?: Record<string, unknown>
  ) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.retryable = retryable;
    this.context = context;

    // Maintain proper stack trace for where our error was thrown
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

export class ConfigurationError extends BaseFrameworkError {
  constructor(message: string, context?: Record<string, unknown>) {
    super('CONFIGURATION_ERROR', message, false, context);
  }
}

export class PluginError extends BaseFrameworkError {
  constructor(message: string, retryable = false, context?: Record<string, unknown>) {
    super('PLUGIN_ERROR', message, retryable, context);
  }
}

export class ToolExecutionError extends BaseFrameworkError {
  constructor(message: string, retryable = true, context?: Record<string, unknown>) {
    super('TOOL_EXECUTION_ERROR', message, retryable, context);
  }
}

export class ResourceError extends BaseFrameworkError {
  constructor(message: string, retryable = true, context?: Record<string, unknown>) {
    super('RESOURCE_ERROR', message, retryable, context);
  }
}

export class IntegrationError extends BaseFrameworkError {
  constructor(message: string, retryable = true, context?: Record<string, unknown>) {
    super('INTEGRATION_ERROR', message, retryable, context);
  }
}

export class ValidationError extends BaseFrameworkError {
  constructor(message: string, context?: Record<string, unknown>) {
    super('VALIDATION_ERROR', message, false, context);
  }
}

/**
 * Error factory for creating framework errors
 */
export class ErrorFactory {
  static configuration(message: string, context?: Record<string, unknown>): ConfigurationError {
    return new ConfigurationError(message, context);
  }

  static plugin(message: string, retryable = false, context?: Record<string, unknown>): PluginError {
    return new PluginError(message, retryable, context);
  }

  static toolExecution(message: string, retryable = true, context?: Record<string, unknown>): ToolExecutionError {
    return new ToolExecutionError(message, retryable, context);
  }

  static resource(message: string, retryable = true, context?: Record<string, unknown>): ResourceError {
    return new ResourceError(message, retryable, context);
  }

  static integration(message: string, retryable = true, context?: Record<string, unknown>): IntegrationError {
    return new IntegrationError(message, retryable, context);
  }

  static validation(message: string, context?: Record<string, unknown>): ValidationError {
    return new ValidationError(message, context);
  }
}

/**
 * Error handler utility
 */
export class ErrorHandler {
  static isFrameworkError(error: unknown): error is FrameworkError {
    return error instanceof BaseFrameworkError;
  }

  static isRetryable(error: unknown): boolean {
    return this.isFrameworkError(error) && error.retryable;
  }

  static getErrorCode(error: unknown): string {
    if (this.isFrameworkError(error)) {
      return error.code;
    }
    return 'UNKNOWN_ERROR';
  }

  static getErrorContext(error: unknown): Record<string, unknown> | undefined {
    if (this.isFrameworkError(error)) {
      return error.context;
    }
    return undefined;
  }

  static formatError(error: unknown): string {
    if (this.isFrameworkError(error)) {
      const context = error.context ? ` (${JSON.stringify(error.context)})` : '';
      return `[${error.code}] ${error.message}${context}`;
    }
    
    if (error instanceof Error) {
      return error.message;
    }
    
    return String(error);
  }
}
