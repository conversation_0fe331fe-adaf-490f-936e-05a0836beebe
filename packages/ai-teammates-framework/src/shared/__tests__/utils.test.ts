/**
 * Utils tests
 */

import {
  deepMerge,
  isObject,
  getNestedValue,
  setNestedValue,
  isDefined,
  ensureArray,
  sleep,
  retry,
  debounce,
  throttle,
  timeout,
  safeJsonParse,
  safeJsonStringify,
  generateUUID,
  sanitizeFilename,
  formatBytes
} from '../utils.js';

describe('Utils', () => {
  describe('deepMerge', () => {
    test('should merge objects deeply', () => {
      const target = { a: 1, b: { c: 2, d: 3 } };
      const source = { b: { c: 4, e: 5 }, f: 6 };
      const result = deepMerge(target, source);
      
      expect(result).toEqual({
        a: 1,
        b: { c: 4, d: 3, e: 5 },
        f: 6
      });
    });

    test('should not mutate original objects', () => {
      const target = { a: 1, b: { c: 2 } };
      const source = { b: { d: 3 } };
      const result = deepMerge(target, source);
      
      expect(target).toEqual({ a: 1, b: { c: 2 } });
      expect(source).toEqual({ b: { d: 3 } });
      expect(result).toEqual({ a: 1, b: { c: 2, d: 3 } });
    });
  });

  describe('isObject', () => {
    test('should identify objects correctly', () => {
      expect(isObject({})).toBe(true);
      expect(isObject({ a: 1 })).toBe(true);
      expect(isObject([])).toBe(false);
      expect(isObject(null)).toBe(false);
      expect(isObject(undefined)).toBe(false);
      expect(isObject('string')).toBe(false);
      expect(isObject(123)).toBe(false);
    });
  });

  describe('getNestedValue', () => {
    test('should get nested values', () => {
      const obj = { a: { b: { c: 'value' } } };
      expect(getNestedValue(obj, 'a.b.c')).toBe('value');
      expect(getNestedValue(obj, 'a.b')).toEqual({ c: 'value' });
      expect(getNestedValue(obj, 'a.b.d')).toBeUndefined();
    });
  });

  describe('setNestedValue', () => {
    test('should set nested values', () => {
      const obj: any = {};
      setNestedValue(obj, 'a.b.c', 'value');
      expect(obj).toEqual({ a: { b: { c: 'value' } } });
    });
  });

  describe('isDefined', () => {
    test('should check if value is defined', () => {
      expect(isDefined('value')).toBe(true);
      expect(isDefined(0)).toBe(true);
      expect(isDefined(false)).toBe(true);
      expect(isDefined(null)).toBe(false);
      expect(isDefined(undefined)).toBe(false);
    });
  });

  describe('ensureArray', () => {
    test('should ensure value is array', () => {
      expect(ensureArray('value')).toEqual(['value']);
      expect(ensureArray(['value'])).toEqual(['value']);
      expect(ensureArray(123)).toEqual([123]);
    });
  });

  describe('safeJsonParse', () => {
    test('should parse valid JSON', () => {
      expect(safeJsonParse('{"a": 1}', {})).toEqual({ a: 1 });
    });

    test('should return fallback for invalid JSON', () => {
      expect(safeJsonParse('invalid', { fallback: true })).toEqual({ fallback: true });
    });
  });

  describe('safeJsonStringify', () => {
    test('should stringify objects', () => {
      expect(safeJsonStringify({ a: 1 })).toBe('{"a":1}');
    });

    test('should handle circular references', () => {
      const obj: any = { a: 1 };
      obj.self = obj;
      const result = safeJsonStringify(obj);
      expect(typeof result).toBe('string');
    });
  });

  describe('generateUUID', () => {
    test('should generate valid UUID', () => {
      const uuid = generateUUID();
      expect(uuid).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });

    test('should generate unique UUIDs', () => {
      const uuid1 = generateUUID();
      const uuid2 = generateUUID();
      expect(uuid1).not.toBe(uuid2);
    });
  });

  describe('sanitizeFilename', () => {
    test('should sanitize filenames', () => {
      expect(sanitizeFilename('hello world.txt')).toBe('hello_world.txt');
      expect(sanitizeFilename('file/with\\special:chars')).toBe('file_with_special_chars');
      expect(sanitizeFilename('___multiple___underscores___')).toBe('multiple_underscores');
    });
  });

  describe('formatBytes', () => {
    test('should format bytes correctly', () => {
      expect(formatBytes(0)).toBe('0 Bytes');
      expect(formatBytes(1024)).toBe('1 KB');
      expect(formatBytes(1024 * 1024)).toBe('1 MB');
      expect(formatBytes(1536)).toBe('1.5 KB');
    });
  });

  describe('sleep', () => {
    test('should resolve after specified time', async () => {
      const start = Date.now();
      await sleep(10);
      const end = Date.now();
      expect(end - start).toBeGreaterThanOrEqual(10);
    });
  });

  describe('retry', () => {
    test('should succeed on first try', async () => {
      let callCount = 0;
      const fn = async () => {
        callCount++;
        return 'success';
      };
      const result = await retry(fn);
      expect(result).toBe('success');
      expect(callCount).toBe(1);
    });

    test('should retry on failure', async () => {
      let callCount = 0;
      const fn = async () => {
        callCount++;
        if (callCount === 1) {
          throw new Error('fail');
        }
        return 'success';
      };

      const result = await retry(fn, { maxAttempts: 2, baseDelay: 1 });
      expect(result).toBe('success');
      expect(callCount).toBe(2);
    });

    test('should throw after max attempts', async () => {
      let callCount = 0;
      const fn = async () => {
        callCount++;
        throw new Error('fail');
      };

      await expect(retry(fn, { maxAttempts: 2, baseDelay: 1 })).rejects.toThrow('fail');
      expect(callCount).toBe(2);
    });
  });

  describe('debounce', () => {
    test('should debounce function calls', async () => {
      let callCount = 0;
      let lastArgs: any;
      const fn = (...args: any[]) => {
        callCount++;
        lastArgs = args;
      };
      const debounced = debounce(fn, 10);

      debounced('arg1');
      debounced('arg2');
      debounced('arg3');

      expect(callCount).toBe(0);

      await sleep(15);
      expect(callCount).toBe(1);
      expect(lastArgs).toEqual(['arg3']);
    });
  });

  describe('throttle', () => {
    test('should throttle function calls', async () => {
      let callCount = 0;
      let firstArgs: any;
      const fn = (...args: any[]) => {
        callCount++;
        if (callCount === 1) {
          firstArgs = args;
        }
      };
      const throttled = throttle(fn, 10);

      throttled('arg1');
      throttled('arg2');
      throttled('arg3');

      expect(callCount).toBe(1);
      expect(firstArgs).toEqual(['arg1']);
    });

    test('should handle edge case with zero delay', () => {
      let callCount = 0;
      const fn = () => callCount++;
      const throttled = throttle(fn, 0);

      throttled();
      throttled();

      expect(callCount).toBe(1);
    });
  });

  describe('timeout', () => {
    test('should resolve if promise completes in time', async () => {
      const promise = Promise.resolve('success');
      const result = await timeout(promise, 100);
      expect(result).toBe('success');
    });

    test('should reject if promise times out', async () => {
      const promise = new Promise(resolve => setTimeout(() => resolve('late'), 100));
      await expect(timeout(promise, 10)).rejects.toThrow('Operation timed out after 10ms');
    });
  });
});
