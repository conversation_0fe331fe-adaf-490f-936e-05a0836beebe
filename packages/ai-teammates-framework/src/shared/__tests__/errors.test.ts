/**
 * Errors tests
 */

import {
  BaseFrameworkError,
  ConfigurationError,
  PluginError,
  ToolExecutionError,
  ResourceError,
  IntegrationError,
  ValidationError,
  ErrorFactory,
  ErrorHandler
} from '../errors.js';

describe('Framework Errors', () => {
  describe('BaseFrameworkError', () => {
    test('should create error with all properties', () => {
      const context = { key: 'value' };
      const error = new BaseFrameworkError('TEST_CODE', 'Test message', true, context);
      
      expect(error.code).toBe('TEST_CODE');
      expect(error.message).toBe('Test message');
      expect(error.retryable).toBe(true);
      expect(error.context).toEqual(context);
      expect(error.name).toBe('BaseFrameworkError');
    });

    test('should default retryable to false', () => {
      const error = new BaseFrameworkError('TEST_CODE', 'Test message');
      expect(error.retryable).toBe(false);
    });

    test('should maintain stack trace', () => {
      const error = new BaseFrameworkError('TEST_CODE', 'Test message');
      expect(error.stack).toBeDefined();
    });
  });

  describe('Specific Error Types', () => {
    test('ConfigurationError should have correct properties', () => {
      const context = { field: 'test' };
      const error = new ConfigurationError('Config error', context);
      
      expect(error.code).toBe('CONFIGURATION_ERROR');
      expect(error.message).toBe('Config error');
      expect(error.retryable).toBe(false);
      expect(error.context).toEqual(context);
    });

    test('PluginError should have correct properties', () => {
      const error = new PluginError('Plugin error', true);
      
      expect(error.code).toBe('PLUGIN_ERROR');
      expect(error.retryable).toBe(true);
    });

    test('ToolExecutionError should default to retryable', () => {
      const error = new ToolExecutionError('Tool error');
      
      expect(error.code).toBe('TOOL_EXECUTION_ERROR');
      expect(error.retryable).toBe(true);
    });

    test('ResourceError should default to retryable', () => {
      const error = new ResourceError('Resource error');
      
      expect(error.code).toBe('RESOURCE_ERROR');
      expect(error.retryable).toBe(true);
    });

    test('IntegrationError should default to retryable', () => {
      const error = new IntegrationError('Integration error');
      
      expect(error.code).toBe('INTEGRATION_ERROR');
      expect(error.retryable).toBe(true);
    });

    test('ValidationError should not be retryable', () => {
      const error = new ValidationError('Validation error');
      
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.retryable).toBe(false);
    });
  });

  describe('ErrorFactory', () => {
    test('should create ConfigurationError', () => {
      const context = { field: 'test' };
      const error = ErrorFactory.configuration('Config error', context);
      
      expect(error).toBeInstanceOf(ConfigurationError);
      expect(error.message).toBe('Config error');
      expect(error.context).toEqual(context);
    });

    test('should create PluginError', () => {
      const error = ErrorFactory.plugin('Plugin error', true);
      
      expect(error).toBeInstanceOf(PluginError);
      expect(error.retryable).toBe(true);
    });

    test('should create ToolExecutionError', () => {
      const error = ErrorFactory.toolExecution('Tool error');
      
      expect(error).toBeInstanceOf(ToolExecutionError);
      expect(error.retryable).toBe(true);
    });

    test('should create ResourceError', () => {
      const error = ErrorFactory.resource('Resource error', false);
      
      expect(error).toBeInstanceOf(ResourceError);
      expect(error.retryable).toBe(false);
    });

    test('should create IntegrationError', () => {
      const error = ErrorFactory.integration('Integration error');
      
      expect(error).toBeInstanceOf(IntegrationError);
      expect(error.retryable).toBe(true);
    });

    test('should create ValidationError', () => {
      const error = ErrorFactory.validation('Validation error');
      
      expect(error).toBeInstanceOf(ValidationError);
      expect(error.retryable).toBe(false);
    });
  });

  describe('ErrorHandler', () => {
    test('should identify framework errors', () => {
      const frameworkError = new ConfigurationError('Test');
      const regularError = new Error('Test');
      
      expect(ErrorHandler.isFrameworkError(frameworkError)).toBe(true);
      expect(ErrorHandler.isFrameworkError(regularError)).toBe(false);
    });

    test('should check if error is retryable', () => {
      const retryableError = new PluginError('Test', true);
      const nonRetryableError = new ConfigurationError('Test');
      const regularError = new Error('Test');
      
      expect(ErrorHandler.isRetryable(retryableError)).toBe(true);
      expect(ErrorHandler.isRetryable(nonRetryableError)).toBe(false);
      expect(ErrorHandler.isRetryable(regularError)).toBe(false);
    });

    test('should get error code', () => {
      const frameworkError = new ConfigurationError('Test');
      const regularError = new Error('Test');
      
      expect(ErrorHandler.getErrorCode(frameworkError)).toBe('CONFIGURATION_ERROR');
      expect(ErrorHandler.getErrorCode(regularError)).toBe('UNKNOWN_ERROR');
    });

    test('should get error context', () => {
      const context = { key: 'value' };
      const frameworkError = new ConfigurationError('Test', context);
      const regularError = new Error('Test');
      
      expect(ErrorHandler.getErrorContext(frameworkError)).toEqual(context);
      expect(ErrorHandler.getErrorContext(regularError)).toBeUndefined();
    });

    test('should format framework errors', () => {
      const context = { key: 'value' };
      const frameworkError = new ConfigurationError('Test message', context);
      
      const formatted = ErrorHandler.formatError(frameworkError);
      expect(formatted).toBe('[CONFIGURATION_ERROR] Test message ({"key":"value"})');
    });

    test('should format framework errors without context', () => {
      const frameworkError = new ConfigurationError('Test message');
      
      const formatted = ErrorHandler.formatError(frameworkError);
      expect(formatted).toBe('[CONFIGURATION_ERROR] Test message');
    });

    test('should format regular errors', () => {
      const regularError = new Error('Test message');
      
      const formatted = ErrorHandler.formatError(regularError);
      expect(formatted).toBe('Test message');
    });

    test('should format non-error values', () => {
      const formatted = ErrorHandler.formatError('string error');
      expect(formatted).toBe('string error');
    });
  });
});
