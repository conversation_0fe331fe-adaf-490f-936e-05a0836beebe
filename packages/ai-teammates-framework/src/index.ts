/**
 * AI Teammates Framework
 * Generic, reusable framework for building AI teammates with plugin architecture
 */

// Core exports
export * from './core/index.js';

// Plugin system exports
export * from './plugins/index.js';

// Shared utilities and types
export * from './shared/index.js';

// Main framework class for convenience
export { TeammateEngine as Framework } from './core/teammate-engine.js';
export { ConfigSystem as Config } from './core/config-system.js';
