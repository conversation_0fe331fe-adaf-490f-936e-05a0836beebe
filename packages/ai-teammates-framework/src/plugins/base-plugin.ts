/**
 * Base Plugin
 * Abstract base class for teammate plugins
 */

import type { 
  TeammatePlugin,
  PersonalityConfig,
  ToolDefinition,
  ResourceDefinition,
  PromptDefinition,
  PluginContext
} from '../shared/types.js';

export abstract class BasePlugin implements TeammatePlugin {
  protected context?: PluginContext;

  /**
   * Get plugin name
   */
  abstract getName(): string;

  /**
   * Get plugin role
   */
  abstract getRole(): string;

  /**
   * Get personality configuration
   */
  abstract getPersonality(): PersonalityConfig;

  /**
   * Get tool definitions
   */
  abstract getTools(): ToolDefinition[];

  /**
   * Get resource definitions
   */
  abstract getResources(): ResourceDefinition[];

  /**
   * Get prompt definitions
   */
  abstract getPrompts(): PromptDefinition[];

  /**
   * Initialize plugin with context
   */
  async initialize(context: PluginContext): Promise<void> {
    this.context = context;
    context.logger.info(`Initializing plugin: ${this.getName()}`);
    
    // Override in subclasses for custom initialization
    await this.onInitialize(context);
    
    context.logger.info(`Plugin initialized: ${this.getName()}`);
  }

  /**
   * Override in subclasses for custom initialization logic
   */
  protected async onInitialize(context: PluginContext): Promise<void> {
    // Default implementation does nothing
  }

  /**
   * Get plugin context (available after initialization)
   */
  protected getContext(): PluginContext {
    if (!this.context) {
      throw new Error('Plugin not initialized - context not available');
    }
    return this.context;
  }

  /**
   * Helper method to create tool definition
   */
  protected createTool(
    name: string,
    description: string,
    schema: any,
    handler: (args: any, context: any) => Promise<any>
  ): ToolDefinition {
    return {
      name,
      description,
      schema,
      handler
    };
  }

  /**
   * Helper method to create resource definition
   */
  protected createResource(
    uri: string,
    name: string,
    description: string,
    mimeType: string,
    handler: () => Promise<any>
  ): ResourceDefinition {
    return {
      uri,
      name,
      description,
      mimeType,
      handler
    };
  }

  /**
   * Helper method to create prompt definition
   */
  protected createPrompt(
    name: string,
    description: string,
    template: string,
    args: Array<{
      name: string;
      description: string;
      required: boolean;
      type: 'string' | 'number' | 'boolean' | 'object';
    }> = []
  ): PromptDefinition {
    return {
      name,
      description,
      template,
      arguments: args
    };
  }

  /**
   * Helper method to get agent-specific tool name
   */
  protected getToolName(baseName: string): string {
    const context = this.getContext();
    const prefix = context.config.features.tools.namePrefix;
    return `${prefix}-${baseName}`;
  }

  /**
   * Helper method to get agent-specific URI
   */
  protected getURI(type: 'memory' | 'template' | 'document', resource: string): string {
    const context = this.getContext();
    const uriBuilder = context.uriBuilder;
    
    switch (type) {
      case 'memory':
        return uriBuilder.memory(resource);
      case 'template':
        return uriBuilder.template(resource);
      case 'document':
        return uriBuilder.document(resource);
      default:
        throw new Error(`Unknown URI type: ${type}`);
    }
  }

  /**
   * Helper method to log from plugin
   */
  protected log(level: 'debug' | 'info' | 'warn' | 'error', message: string, context?: Record<string, any>): void {
    const pluginContext = this.getContext();
    const logger = pluginContext.logger;
    
    switch (level) {
      case 'debug':
        logger.debug(message, context);
        break;
      case 'info':
        logger.info(message, context);
        break;
      case 'warn':
        logger.warn(message, context);
        break;
      case 'error':
        logger.error(message, undefined, context);
        break;
    }
  }
}
