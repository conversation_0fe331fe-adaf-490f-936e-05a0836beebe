/**
 * Base Plugin tests
 */

import { BasePlugin } from '../base-plugin.js';
import type { 
  PersonalityConfig, 
  ToolDefinition, 
  ResourceDefinition, 
  PromptDefinition,
  PluginContext,
  TeammateConfig
} from '../../shared/types.js';
import { URIBuilder, MemoryService, Logger } from '../../core/services/index.js';

// Test implementation of BasePlugin
class TestPlugin extends BasePlugin {
  getName(): string {
    return 'test-plugin';
  }

  getRole(): string {
    return 'Test Assistant';
  }

  getPersonality(): PersonalityConfig {
    return {
      avatar: '🧪',
      voice: 'technical',
      expertise: ['testing'],
      communicationStyle: 'technical',
      defaultPrompts: ['test-prompt']
    };
  }

  getTools(): ToolDefinition[] {
    return [
      this.createTool(
        'test-tool',
        'A test tool',
        { type: 'object', properties: {} },
        async () => ({ result: 'test' })
      )
    ];
  }

  getResources(): ResourceDefinition[] {
    return [
      this.createResource(
        'test://resource',
        'Test Resource',
        'A test resource',
        'text/plain',
        async () => ({
          uri: 'test://resource',
          mimeType: 'text/plain',
          text: 'test content'
        })
      )
    ];
  }

  getPrompts(): PromptDefinition[] {
    return [
      this.createPrompt(
        'test-prompt',
        'A test prompt',
        'Hello {{name}}!',
        [
          {
            name: 'name',
            description: 'Name to greet',
            required: true,
            type: 'string'
          }
        ]
      )
    ];
  }

  // Expose protected methods for testing
  public testGetContext() {
    return this.getContext();
  }

  public testGetToolName(baseName: string) {
    return this.getToolName(baseName);
  }

  public testGetURI(type: 'memory' | 'template' | 'document', resource: string) {
    return this.getURI(type, resource);
  }

  public testLog(level: 'debug' | 'info' | 'warn' | 'error', message: string, context?: Record<string, any>) {
    return this.log(level, message, context);
  }
}

describe('BasePlugin', () => {
  let plugin: TestPlugin;
  let mockContext: PluginContext;
  let mockConfig: TeammateConfig;

  beforeEach(() => {
    plugin = new TestPlugin();
    
    mockConfig = {
      agent: {
        name: 'test-agent',
        role: 'Test Agent',
        version: '1.0.0',
        personality: {
          avatar: '🤖',
          voice: 'professional',
          expertise: ['testing'],
          communicationStyle: 'professional',
          defaultPrompts: []
        }
      },
      organization: {
        name: 'test-org',
        defaultRepo: 'test-repo',
        defaultReviewer: 'test-reviewer',
        defaultBranch: 'main'
      },
      integrations: {
        doppler: { token: '', project: '', environment: 'test' },
        storage: { type: 'mock' },
        github: {
          statusNames: {
            humanReview: ['Review'],
            inProgress: ['In Progress'],
            completed: ['Done']
          },
          commentSignature: 'Test Agent'
        }
      },
      transport: { type: 'stdio' },
      features: {
        resources: {
          uriPrefix: 'test-agent',
          conversationHistoryLimit: 50,
          enabledResources: ['memory']
        },
        tools: {
          namePrefix: 'test-agent',
          enabledTools: []
        },
        prompts: {
          enabledPrompts: [],
          templatePath: './prompts'
        }
      }
    };

    mockContext = {
      config: mockConfig,
      uriBuilder: new URIBuilder('test-agent'),
      logger: new Logger({ agentName: 'test-agent' }),
      integrations: {}
    };
  });

  describe('abstract methods implementation', () => {
    test('should implement getName', () => {
      expect(plugin.getName()).toBe('test-plugin');
    });

    test('should implement getRole', () => {
      expect(plugin.getRole()).toBe('Test Assistant');
    });

    test('should implement getPersonality', () => {
      const personality = plugin.getPersonality();
      expect(personality.avatar).toBe('🧪');
      expect(personality.communicationStyle).toBe('technical');
    });

    test('should implement getTools', () => {
      const tools = plugin.getTools();
      expect(tools).toHaveLength(1);
      expect(tools[0].name).toBe('test-tool');
    });

    test('should implement getResources', () => {
      const resources = plugin.getResources();
      expect(resources).toHaveLength(1);
      expect(resources[0].name).toBe('Test Resource');
    });

    test('should implement getPrompts', () => {
      const prompts = plugin.getPrompts();
      expect(prompts).toHaveLength(1);
      expect(prompts[0].name).toBe('test-prompt');
    });
  });

  describe('initialization', () => {
    test('should initialize with context', async () => {
      await plugin.initialize(mockContext);
      
      const context = plugin.testGetContext();
      expect(context).toBe(mockContext);
    });

    test('should throw error when accessing context before initialization', () => {
      expect(() => plugin.testGetContext()).toThrow('Plugin not initialized');
    });
  });

  describe('helper methods', () => {
    beforeEach(async () => {
      await plugin.initialize(mockContext);
    });

    test('should create tool definition', () => {
      const tool = plugin.getTools()[0];
      expect(tool).toEqual({
        name: 'test-tool',
        description: 'A test tool',
        schema: { type: 'object', properties: {} },
        handler: expect.any(Function)
      });
    });

    test('should create resource definition', () => {
      const resource = plugin.getResources()[0];
      expect(resource).toEqual({
        uri: 'test://resource',
        name: 'Test Resource',
        description: 'A test resource',
        mimeType: 'text/plain',
        handler: expect.any(Function)
      });
    });

    test('should create prompt definition', () => {
      const prompt = plugin.getPrompts()[0];
      expect(prompt).toEqual({
        name: 'test-prompt',
        description: 'A test prompt',
        template: 'Hello {{name}}!',
        arguments: [
          {
            name: 'name',
            description: 'Name to greet',
            required: true,
            type: 'string'
          }
        ]
      });
    });

    test('should get tool name with prefix', () => {
      const toolName = plugin.testGetToolName('my-tool');
      expect(toolName).toBe('test-agent-my-tool');
    });

    test('should get URIs', () => {
      expect(plugin.testGetURI('memory', 'test')).toBe('memory://test-agent/test');
      expect(plugin.testGetURI('template', 'test')).toBe('template://test-agent/test');
      expect(plugin.testGetURI('document', 'test')).toBe('file://test-agent/documents/test');
    });

    test('should throw error for unknown URI type', () => {
      expect(() => plugin.testGetURI('unknown' as any, 'test')).toThrow('Unknown URI type: unknown');
    });

    test('should log messages', () => {
      const loggerSpy = jest.spyOn(mockContext.logger, 'info');
      plugin.testLog('info', 'test message', { key: 'value' });
      expect(loggerSpy).toHaveBeenCalledWith('test message', { key: 'value' });
    });

    test('should log debug messages', () => {
      const loggerSpy = jest.spyOn(mockContext.logger, 'debug');
      plugin.testLog('debug', 'debug message');
      expect(loggerSpy).toHaveBeenCalledWith('debug message', undefined);
    });

    test('should log warning messages', () => {
      const loggerSpy = jest.spyOn(mockContext.logger, 'warn');
      plugin.testLog('warn', 'warning message');
      expect(loggerSpy).toHaveBeenCalledWith('warning message', undefined);
    });

    test('should log error messages', () => {
      const loggerSpy = jest.spyOn(mockContext.logger, 'error');
      plugin.testLog('error', 'error message');
      expect(loggerSpy).toHaveBeenCalledWith('error message', undefined, undefined);
    });
  });

  describe('tool execution', () => {
    test('should execute tool handler', async () => {
      const tools = plugin.getTools();
      const result = await tools[0].handler({}, {} as any);
      expect(result).toEqual({ result: 'test' });
    });
  });

  describe('resource execution', () => {
    test('should execute resource handler', async () => {
      const resources = plugin.getResources();
      const result = await resources[0].handler();
      expect(result).toEqual({
        uri: 'test://resource',
        mimeType: 'text/plain',
        text: 'test content'
      });
    });
  });
});
