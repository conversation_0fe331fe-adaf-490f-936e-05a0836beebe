/**
 * Teammate Engine
 * Core orchestrator for AI teammates with plugin architecture
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';

import type { 
  TeammateEngine as ITeammateEngine,
  TeammateConfig,
  TeammatePlugin,
  PluginContext,
  ExecutionContext,
  IntegrationServices
} from '../shared/types.js';

import { ErrorFactory } from '../shared/errors.js';
import { URIBuilder, MemoryService, Logger } from './services/index.js';

export class TeammateEngine implements ITeammateEngine {
  private server: Server;
  private plugin: TeammatePlugin;
  private config: TeammateConfig;
  private uriBuilder: URIBuilder;
  private memory: MemoryService;
  private logger: Logger;
  private integrations: IntegrationServices = {};
  private initialized = false;

  constructor(config: TeammateConfig, plugin: TeammatePlugin) {
    this.config = config;
    this.plugin = plugin;
    
    // Initialize core services
    this.uriBuilder = new URIBuilder(config.features.resources.uriPrefix);
    this.memory = new MemoryService();
    this.logger = new Logger({
      agentName: config.agent.name,
      logLevel: process.env.LOG_LEVEL as any || 'info',
      enableConsole: true
    });

    // Initialize MCP server
    this.server = new Server(
      {
        name: config.agent.name,
        version: config.agent.version
      },
      {
        capabilities: {
          tools: {},
          resources: {},
          prompts: {}
        }
      }
    );

    this.logger.info('Teammate engine created', {
      agent: config.agent.name,
      role: config.agent.role,
      plugin: plugin.getName()
    });
  }

  /**
   * Initialize the teammate engine
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      this.logger.warn('Engine already initialized');
      return;
    }

    try {
      this.logger.info('Initializing teammate engine...');

      // Initialize integrations
      await this.initializeIntegrations();

      // Create plugin context
      const pluginContext: PluginContext = {
        config: this.config,
        uriBuilder: this.uriBuilder,
        logger: this.logger.child({ component: 'plugin' }),
        integrations: this.integrations
      };

      // Initialize plugin
      await this.plugin.initialize(pluginContext);

      // Setup MCP handlers
      await this.setupTools();
      await this.setupResources();
      await this.setupPrompts();

      this.initialized = true;
      this.logger.info('Teammate engine initialized successfully');

    } catch (error) {
      const message = 'Failed to initialize teammate engine';
      this.logger.error(message, error as Error);
      throw ErrorFactory.plugin(message, false, { error: error instanceof Error ? error.message : String(error) });
    }
  }

  /**
   * Start the teammate engine
   */
  async start(): Promise<void> {
    if (!this.initialized) {
      throw ErrorFactory.plugin('Engine must be initialized before starting');
    }

    try {
      this.logger.info('Starting teammate engine...');

      // Create transport based on configuration
      const transport = this.createTransport();

      // Connect server to transport
      await this.server.connect(transport);

      this.logger.info('Teammate engine started successfully', {
        transport: this.config.transport.type
      });

    } catch (error) {
      const message = 'Failed to start teammate engine';
      this.logger.error(message, error as Error);
      throw ErrorFactory.plugin(message, true, { error: error instanceof Error ? error.message : String(error) });
    }
  }

  /**
   * Stop the teammate engine
   */
  async stop(): Promise<void> {
    try {
      this.logger.info('Stopping teammate engine...');
      
      // Close server connection
      await this.server.close();
      
      this.logger.info('Teammate engine stopped successfully');
    } catch (error) {
      const message = 'Failed to stop teammate engine';
      this.logger.error(message, error as Error);
      throw ErrorFactory.plugin(message, false, { error: error instanceof Error ? error.message : String(error) });
    }
  }

  /**
   * Get the MCP server instance
   */
  getServer(): Server {
    return this.server;
  }

  /**
   * Initialize integration services
   */
  private async initializeIntegrations(): Promise<void> {
    this.logger.debug('Initializing integrations...');

    // For now, we'll use mock integrations
    // TODO: Add integration service support when available
    this.logger.info('Using mock integrations - integration service will be added later');

    this.logger.debug('Integrations initialized');
  }

  /**
   * Setup tool handlers
   */
  private async setupTools(): Promise<void> {
    const tools = this.plugin.getTools();
    
    this.logger.debug('Setting up tools', { count: tools.length });

    // Set up tool call handler
    this.server.setRequestHandler({ method: 'tools/call' }, async (request: any) => {
      const toolName = request.params?.name;
      const tool = tools.find(t => t.name === toolName);

      if (!tool) {
        return {
          content: [{
            type: 'text',
            text: `Tool not found: ${toolName}`
          }],
          isError: true
        };
      }

      const startTime = Date.now();

      try {
        this.logger.debug('Executing tool', { tool: tool.name, args: request.params?.arguments });

        // Create execution context
        const context: ExecutionContext = {
          config: this.config,
          uriBuilder: this.uriBuilder,
          logger: this.logger.child({ tool: tool.name }),
          memory: this.memory,
          storage: this.integrations.storage,
          integrations: this.integrations
        };

        // Execute tool
        const result = await tool.handler(request.params?.arguments || {}, context);

        const duration = Date.now() - startTime;
        this.logger.info('Tool executed successfully', {
          tool: tool.name,
          duration: `${duration}ms`
        });

        return { content: [{ type: 'text', text: JSON.stringify(result, null, 2) }] };

      } catch (error) {
        const duration = Date.now() - startTime;
        this.logger.error('Tool execution failed', error as Error, {
          tool: tool.name,
          duration: `${duration}ms`
        });

        return {
          content: [{
            type: 'text',
            text: `Error executing tool ${tool.name}: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    });

    // List tools handler
    this.server.setRequestHandler({ method: 'tools/list' }, async () => {
      return {
        tools: tools.map(tool => ({
          name: tool.name,
          description: tool.description,
          inputSchema: tool.schema
        }))
      };
    });

    this.logger.info('Tools setup completed', { count: tools.length });
  }

  /**
   * Setup resource handlers
   */
  private async setupResources(): Promise<void> {
    const resources = this.plugin.getResources();
    
    this.logger.debug('Setting up resources', { count: resources.length });

    // List resources handler
    this.server.setRequestHandler({ method: 'resources/list' }, async () => {
      const resourceList = [];

      for (const resource of resources) {
        resourceList.push({
          uri: resource.uri,
          name: resource.name,
          description: resource.description,
          mimeType: resource.mimeType
        });
      }

      return { resources: resourceList };
    });

    // Read resource handler
    this.server.setRequestHandler({ method: 'resources/read' }, async (request: any) => {
      const uri = request.params?.uri;

      try {
        this.logger.debug('Reading resource', { uri });

        // Find matching resource
        const resource = resources.find(r => r.uri === uri);
        if (!resource) {
          throw new Error(`Resource not found: ${uri}`);
        }

        // Execute resource handler
        const content = await resource.handler();

        this.logger.debug('Resource read successfully', { uri });
        return { contents: [content] };

      } catch (error) {
        this.logger.error('Failed to read resource', error as Error, { uri });
        throw error;
      }
    });

    this.logger.info('Resources setup completed', { count: resources.length });
  }

  /**
   * Setup prompt handlers
   */
  private async setupPrompts(): Promise<void> {
    const prompts = this.plugin.getPrompts();
    
    this.logger.debug('Setting up prompts', { count: prompts.length });

    // List prompts handler
    this.server.setRequestHandler({ method: 'prompts/list' }, async () => {
      return {
        prompts: prompts.map(prompt => ({
          name: prompt.name,
          description: prompt.description,
          arguments: prompt.arguments
        }))
      };
    });

    // Get prompt handler
    this.server.setRequestHandler({ method: 'prompts/get' }, async (request: any) => {
      const name = request.params?.name;
      const args = request.params?.arguments || {};

      try {
        this.logger.debug('Getting prompt', { name, args });

        // Find matching prompt
        const prompt = prompts.find(p => p.name === name);
        if (!prompt) {
          throw new Error(`Prompt not found: ${name}`);
        }

        // Process template with arguments
        let processedTemplate = prompt.template;
        for (const [key, value] of Object.entries(args)) {
          const placeholder = `{{${key}}}`;
          processedTemplate = processedTemplate.replace(new RegExp(placeholder, 'g'), String(value));
        }

        this.logger.debug('Prompt processed successfully', { name });

        return {
          description: prompt.description,
          messages: [
            {
              role: 'user',
              content: {
                type: 'text',
                text: processedTemplate
              }
            }
          ]
        };

      } catch (error) {
        this.logger.error('Failed to get prompt', error as Error, { name });
        throw error;
      }
    });

    this.logger.info('Prompts setup completed', { count: prompts.length });
  }

  /**
   * Create transport based on configuration
   */
  private createTransport() {
    switch (this.config.transport.type) {
      case 'stdio':
        return new StdioServerTransport();

      case 'sse':
        // SSE transport requires endpoint and response object
        // For now, we'll fall back to stdio if SSE is requested
        this.logger.warn('SSE transport not fully implemented, falling back to stdio');
        return new StdioServerTransport();

      default:
        throw ErrorFactory.configuration(`Unsupported transport type: ${this.config.transport.type}`);
    }
  }
}
