/**
 * Configuration System
 * Zero hardcoded values, environment-driven configuration
 */

import type { 
  TeammateConfig, 
  ValidationResult, 
  ValidationError 
} from '../shared/types.js';
import { ErrorFactory } from '../shared/errors.js';
import { getNestedValue, isDefined } from '../shared/utils.js';

export class ConfigSystem {
  /**
   * Load configuration from environment variables
   */
  static loadFromEnvironment(): TeammateConfig {
    const config: TeammateConfig = {
      agent: {
        name: process.env.AGENT_NAME || 'ai-teammate',
        role: process.env.AGENT_ROLE || 'AI Assistant',
        version: process.env.npm_package_version || process.env.AGENT_VERSION || '1.0.0',
        personality: {
          avatar: process.env.AGENT_AVATAR || '🤖',
          voice: process.env.AGENT_VOICE || 'professional',
          expertise: (process.env.AGENT_EXPERTISE || 'general').split(',').map(s => s.trim()),
          communicationStyle: (process.env.AGENT_COMMUNICATION_STYLE || 'professional') as any,
          defaultPrompts: (process.env.AGENT_DEFAULT_PROMPTS || '').split(',').map(s => s.trim()).filter(Boolean)
        }
      },
      organization: {
        name: process.env.ORG_NAME || '',
        defaultRepo: process.env.ORG_DEFAULT_REPO || '',
        defaultReviewer: process.env.ORG_DEFAULT_REVIEWER || '',
        defaultBranch: process.env.ORG_DEFAULT_BRANCH || 'main'
      },
      integrations: {
        doppler: {
          token: process.env.DOPPLER_TOKEN || '',
          project: process.env.DOPPLER_PROJECT || '',
          environment: process.env.DOPPLER_ENV || 'prd'
        },
        storage: {
          type: (process.env.STORAGE_TYPE || 'mock') as any,
          basePath: process.env.STORAGE_BASE_PATH || './output',
          github: {
            owner: process.env.GITHUB_OWNER || process.env.ORG_NAME || '',
            repo: process.env.GITHUB_REPO || process.env.ORG_DEFAULT_REPO || '',
            branch: process.env.GITHUB_BRANCH || process.env.ORG_DEFAULT_BRANCH || 'main'
          }
        },
        github: {
          statusNames: {
            humanReview: (process.env.GITHUB_STATUS_HUMAN_REVIEW || 'Human Review Required,Review Required,In Review').split(',').map(s => s.trim()),
            inProgress: (process.env.GITHUB_STATUS_IN_PROGRESS || 'In Progress,Working').split(',').map(s => s.trim()),
            completed: (process.env.GITHUB_STATUS_COMPLETED || 'Done,Completed').split(',').map(s => s.trim())
          },
          commentSignature: process.env.GITHUB_COMMENT_SIGNATURE || `Generated by AI Teammate`
        }
      },
      transport: {
        type: (process.env.TRANSPORT_TYPE || 'stdio') as any,
        options: this.parseTransportOptions()
      },
      features: {
        resources: {
          uriPrefix: process.env.RESOURCE_URI_PREFIX || process.env.AGENT_NAME?.toLowerCase() || 'ai-teammate',
          conversationHistoryLimit: parseInt(process.env.CONVERSATION_HISTORY_LIMIT || '50'),
          enabledResources: (process.env.ENABLED_RESOURCES || 'memory,conversations,templates,documents').split(',').map(s => s.trim())
        },
        tools: {
          namePrefix: process.env.TOOL_NAME_PREFIX || process.env.AGENT_NAME?.toLowerCase() || 'ai-teammate',
          enabledTools: (process.env.ENABLED_TOOLS || '').split(',').map(s => s.trim()).filter(Boolean)
        },
        prompts: {
          enabledPrompts: (process.env.ENABLED_PROMPTS || '').split(',').map(s => s.trim()).filter(Boolean),
          templatePath: process.env.PROMPT_TEMPLATE_PATH || './prompts'
        }
      }
    };

    // Update GitHub comment signature with agent info
    if (!process.env.GITHUB_COMMENT_SIGNATURE) {
      config.integrations.github.commentSignature = `Generated by ${config.agent.name} - ${config.agent.role}`;
    }

    return config;
  }

  /**
   * Parse transport options from environment
   */
  private static parseTransportOptions(): Record<string, any> {
    const options: Record<string, any> = {};
    
    if (process.env.TRANSPORT_OPTIONS) {
      try {
        return JSON.parse(process.env.TRANSPORT_OPTIONS);
      } catch (error) {
        console.warn('Failed to parse TRANSPORT_OPTIONS, using defaults');
      }
    }

    return options;
  }

  /**
   * Validate configuration
   */
  static validate(config: TeammateConfig): ValidationResult {
    const errors: ValidationError[] = [];

    // Required fields
    const requiredFields = [
      'agent.name',
      'agent.role',
      'organization.name'
    ];

    for (const field of requiredFields) {
      const value = getNestedValue(config, field);
      if (!isDefined(value) || value === '') {
        errors.push({
          path: field,
          message: `${field} is required`,
          value
        });
      }
    }

    // Validate agent name format
    if (config.agent.name && !/^[a-zA-Z0-9-_]+$/.test(config.agent.name)) {
      errors.push({
        path: 'agent.name',
        message: 'Agent name must contain only alphanumeric characters, hyphens, and underscores',
        value: config.agent.name
      });
    }

    // Validate communication style
    const validStyles = ['professional', 'casual', 'technical', 'friendly'];
    if (!validStyles.includes(config.agent.personality.communicationStyle)) {
      errors.push({
        path: 'agent.personality.communicationStyle',
        message: `Communication style must be one of: ${validStyles.join(', ')}`,
        value: config.agent.personality.communicationStyle
      });
    }

    // Validate storage type
    const validStorageTypes = ['mock', 'file', 'github'];
    if (!validStorageTypes.includes(config.integrations.storage.type)) {
      errors.push({
        path: 'integrations.storage.type',
        message: `Storage type must be one of: ${validStorageTypes.join(', ')}`,
        value: config.integrations.storage.type
      });
    }

    // Validate transport type
    const validTransportTypes = ['stdio', 'sse'];
    if (!validTransportTypes.includes(config.transport.type)) {
      errors.push({
        path: 'transport.type',
        message: `Transport type must be one of: ${validTransportTypes.join(', ')}`,
        value: config.transport.type
      });
    }

    // Validate conversation history limit
    if (config.features.resources.conversationHistoryLimit < 1) {
      errors.push({
        path: 'features.resources.conversationHistoryLimit',
        message: 'Conversation history limit must be at least 1',
        value: config.features.resources.conversationHistoryLimit
      });
    }

    // Conditional validation for GitHub storage
    if (config.integrations.storage.type === 'github') {
      if (!config.integrations.storage.github?.owner) {
        errors.push({
          path: 'integrations.storage.github.owner',
          message: 'GitHub owner is required when using GitHub storage',
          value: config.integrations.storage.github?.owner
        });
      }
      if (!config.integrations.storage.github?.repo) {
        errors.push({
          path: 'integrations.storage.github.repo',
          message: 'GitHub repo is required when using GitHub storage',
          value: config.integrations.storage.github?.repo
        });
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Load and validate configuration
   */
  static loadAndValidate(): TeammateConfig {
    const config = this.loadFromEnvironment();
    const validation = this.validate(config);

    if (!validation.valid) {
      const errorMessages = validation.errors.map(e => `${e.path}: ${e.message}`).join('\n');
      throw ErrorFactory.configuration(
        `Configuration validation failed:\n${errorMessages}`,
        { errors: validation.errors }
      );
    }

    return config;
  }

  /**
   * Get configuration value by path
   */
  static getValue<T>(config: TeammateConfig, path: string): T {
    return getNestedValue(config, path);
  }

  /**
   * Check if configuration has value at path
   */
  static hasValue(config: TeammateConfig, path: string): boolean {
    const value = getNestedValue(config, path);
    return isDefined(value) && value !== '';
  }
}
