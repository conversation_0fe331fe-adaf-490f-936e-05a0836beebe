/**
 * Config System tests
 */

import { ConfigSystem } from '../config-system.js';

describe('ConfigSystem', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('loadFromEnvironment', () => {
    test('should load default configuration', () => {
      const config = ConfigSystem.loadFromEnvironment();
      
      expect(config.agent.name).toBe('ai-teammate');
      expect(config.agent.role).toBe('AI Assistant');
      expect(config.organization.defaultBranch).toBe('main');
      expect(config.integrations.storage.type).toBe('mock');
      expect(config.transport.type).toBe('stdio');
    });

    test('should load from environment variables', () => {
      process.env.AGENT_NAME = 'test-agent';
      process.env.AGENT_ROLE = 'Test Assistant';
      process.env.ORG_NAME = 'test-org';
      process.env.STORAGE_TYPE = 'file';
      process.env.TRANSPORT_TYPE = 'sse';
      
      const config = ConfigSystem.loadFromEnvironment();
      
      expect(config.agent.name).toBe('test-agent');
      expect(config.agent.role).toBe('Test Assistant');
      expect(config.organization.name).toBe('test-org');
      expect(config.integrations.storage.type).toBe('file');
      expect(config.transport.type).toBe('sse');
    });

    test('should parse array values from environment', () => {
      process.env.AGENT_EXPERTISE = 'skill1,skill2,skill3';
      process.env.ENABLED_RESOURCES = 'memory,templates';
      
      const config = ConfigSystem.loadFromEnvironment();
      
      expect(config.agent.personality.expertise).toEqual(['skill1', 'skill2', 'skill3']);
      expect(config.features.resources.enabledResources).toEqual(['memory', 'templates']);
    });
  });

  describe('validate', () => {
    test('should validate required fields', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.agent.name = '';
      config.organization.name = '';
      
      const result = ConfigSystem.validate(config);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(2);
      expect(result.errors[0].path).toBe('agent.name');
      expect(result.errors[1].path).toBe('organization.name');
    });

    test('should validate agent name format', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.agent.name = 'invalid name!';
      config.organization.name = 'test-org';
      
      const result = ConfigSystem.validate(config);
      
      expect(result.valid).toBe(false);
      expect(result.errors[0].path).toBe('agent.name');
      expect(result.errors[0].message).toContain('alphanumeric');
    });

    test('should validate communication style', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.agent.name = 'test-agent';
      config.organization.name = 'test-org';
      config.agent.personality.communicationStyle = 'invalid' as any;
      
      const result = ConfigSystem.validate(config);
      
      expect(result.valid).toBe(false);
      expect(result.errors[0].path).toBe('agent.personality.communicationStyle');
    });

    test('should validate storage type', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.agent.name = 'test-agent';
      config.organization.name = 'test-org';
      config.integrations.storage.type = 'invalid' as any;
      
      const result = ConfigSystem.validate(config);
      
      expect(result.valid).toBe(false);
      expect(result.errors[0].path).toBe('integrations.storage.type');
    });

    test('should validate GitHub storage requirements', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.agent.name = 'test-agent';
      config.organization.name = 'test-org';
      config.integrations.storage.type = 'github';
      config.integrations.storage.github = { owner: '', repo: '', branch: 'main' };
      
      const result = ConfigSystem.validate(config);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(2);
      expect(result.errors[0].path).toBe('integrations.storage.github.owner');
      expect(result.errors[1].path).toBe('integrations.storage.github.repo');
    });

    test('should pass validation with valid config', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.agent.name = 'test-agent';
      config.organization.name = 'test-org';
      
      const result = ConfigSystem.validate(config);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('loadAndValidate', () => {
    test('should throw on invalid configuration', () => {
      process.env.AGENT_NAME = '';
      
      expect(() => ConfigSystem.loadAndValidate()).toThrow('Configuration validation failed');
    });

    test('should return valid configuration', () => {
      process.env.AGENT_NAME = 'test-agent';
      process.env.ORG_NAME = 'test-org';
      
      const config = ConfigSystem.loadAndValidate();
      
      expect(config.agent.name).toBe('test-agent');
      expect(config.organization.name).toBe('test-org');
    });
  });

  describe('getValue', () => {
    test('should get nested values', () => {
      const config = ConfigSystem.loadFromEnvironment();
      
      expect(ConfigSystem.getValue(config, 'agent.name')).toBe('ai-teammate');
      expect(ConfigSystem.getValue(config, 'integrations.storage.type')).toBe('mock');
    });
  });

  describe('hasValue', () => {
    test('should check if value exists', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.organization.name = 'test-org';

      expect(ConfigSystem.hasValue(config, 'agent.name')).toBe(true);
      expect(ConfigSystem.hasValue(config, 'organization.name')).toBe(true);
      expect(ConfigSystem.hasValue(config, 'nonexistent.path')).toBe(false);
    });
  });

  describe('error handling', () => {
    test('should handle invalid transport type', () => {
      process.env.TRANSPORT_TYPE = 'invalid';

      const config = ConfigSystem.loadFromEnvironment();

      expect(config.transport.type).toBe('invalid'); // Should use provided value
    });

    test('should handle missing environment variables', () => {
      delete process.env.AGENT_NAME;
      delete process.env.AGENT_ROLE;
      delete process.env.ORG_NAME;

      const config = ConfigSystem.loadFromEnvironment();

      expect(config.agent.name).toBe('ai-teammate'); // Should fallback to default
      expect(config.agent.role).toBe('AI Assistant'); // Should fallback to default
      expect(config.organization.name).toBe(''); // Should fallback to empty string
    });

    test('should handle invalid communication style', () => {
      process.env.AGENT_COMMUNICATION_STYLE = 'invalid';

      const config = ConfigSystem.loadFromEnvironment();

      expect(config.agent.personality.communicationStyle).toBe('invalid'); // Should use provided value
    });

    test('should handle invalid transport options JSON', () => {
      process.env.TRANSPORT_OPTIONS = 'invalid json';

      const config = ConfigSystem.loadFromEnvironment();

      expect(config.transport.options).toEqual({}); // Should fallback to empty object
    });

    test('should parse valid transport options JSON', () => {
      process.env.TRANSPORT_OPTIONS = '{"host": "localhost", "port": 8080}';

      const config = ConfigSystem.loadFromEnvironment();

      expect(config.transport.options).toEqual({ host: 'localhost', port: 8080 });
    });
  });

  describe('validation', () => {
    test('should validate invalid transport type', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.transport.type = 'invalid' as any;

      const result = ConfigSystem.validate(config);

      expect(result.valid).toBe(false);
      expect(result.errors).toContainEqual({
        path: 'transport.type',
        message: 'Transport type must be one of: stdio, sse',
        value: 'invalid'
      });
    });

    test('should validate invalid conversation history limit', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.features.resources.conversationHistoryLimit = 0;

      const result = ConfigSystem.validate(config);

      expect(result.valid).toBe(false);
      expect(result.errors).toContainEqual({
        path: 'features.resources.conversationHistoryLimit',
        message: 'Conversation history limit must be at least 1',
        value: 0
      });
    });
  });
});
