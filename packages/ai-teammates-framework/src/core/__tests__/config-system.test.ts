/**
 * Config System tests
 */

import { ConfigSystem } from '../config-system.js';

describe('ConfigSystem', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('loadFromEnvironment', () => {
    test('should load default configuration', () => {
      const config = ConfigSystem.loadFromEnvironment();
      
      expect(config.agent.name).toBe('ai-teammate');
      expect(config.agent.role).toBe('AI Assistant');
      expect(config.organization.defaultBranch).toBe('main');
      expect(config.integrations.storage.type).toBe('mock');
      expect(config.transport.type).toBe('stdio');
    });

    test('should load from environment variables', () => {
      process.env.AGENT_NAME = 'test-agent';
      process.env.AGENT_ROLE = 'Test Assistant';
      process.env.ORG_NAME = 'test-org';
      process.env.STORAGE_TYPE = 'file';
      process.env.TRANSPORT_TYPE = 'sse';
      
      const config = ConfigSystem.loadFromEnvironment();
      
      expect(config.agent.name).toBe('test-agent');
      expect(config.agent.role).toBe('Test Assistant');
      expect(config.organization.name).toBe('test-org');
      expect(config.integrations.storage.type).toBe('file');
      expect(config.transport.type).toBe('sse');
    });

    test('should parse array values from environment', () => {
      process.env.AGENT_EXPERTISE = 'skill1,skill2,skill3';
      process.env.ENABLED_RESOURCES = 'memory,templates';
      
      const config = ConfigSystem.loadFromEnvironment();
      
      expect(config.agent.personality.expertise).toEqual(['skill1', 'skill2', 'skill3']);
      expect(config.features.resources.enabledResources).toEqual(['memory', 'templates']);
    });
  });

  describe('validate', () => {
    test('should validate required fields', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.agent.name = '';
      config.organization.name = '';
      
      const result = ConfigSystem.validate(config);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(2);
      expect(result.errors[0].path).toBe('agent.name');
      expect(result.errors[1].path).toBe('organization.name');
    });

    test('should validate agent name format', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.agent.name = 'invalid name!';
      config.organization.name = 'test-org';
      
      const result = ConfigSystem.validate(config);
      
      expect(result.valid).toBe(false);
      expect(result.errors[0].path).toBe('agent.name');
      expect(result.errors[0].message).toContain('alphanumeric');
    });

    test('should validate communication style', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.agent.name = 'test-agent';
      config.organization.name = 'test-org';
      config.agent.personality.communicationStyle = 'invalid' as any;
      
      const result = ConfigSystem.validate(config);
      
      expect(result.valid).toBe(false);
      expect(result.errors[0].path).toBe('agent.personality.communicationStyle');
    });

    test('should validate storage type', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.agent.name = 'test-agent';
      config.organization.name = 'test-org';
      config.integrations.storage.type = 'invalid' as any;
      
      const result = ConfigSystem.validate(config);
      
      expect(result.valid).toBe(false);
      expect(result.errors[0].path).toBe('integrations.storage.type');
    });

    test('should validate GitHub storage requirements', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.agent.name = 'test-agent';
      config.organization.name = 'test-org';
      config.integrations.storage.type = 'github';
      config.integrations.storage.github = { owner: '', repo: '', branch: 'main' };
      
      const result = ConfigSystem.validate(config);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(2);
      expect(result.errors[0].path).toBe('integrations.storage.github.owner');
      expect(result.errors[1].path).toBe('integrations.storage.github.repo');
    });

    test('should pass validation with valid config', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.agent.name = 'test-agent';
      config.organization.name = 'test-org';
      
      const result = ConfigSystem.validate(config);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('loadAndValidate', () => {
    test('should throw on invalid configuration', () => {
      process.env.AGENT_NAME = '';
      
      expect(() => ConfigSystem.loadAndValidate()).toThrow('Configuration validation failed');
    });

    test('should return valid configuration', () => {
      process.env.AGENT_NAME = 'test-agent';
      process.env.ORG_NAME = 'test-org';
      
      const config = ConfigSystem.loadAndValidate();
      
      expect(config.agent.name).toBe('test-agent');
      expect(config.organization.name).toBe('test-org');
    });
  });

  describe('getValue', () => {
    test('should get nested values', () => {
      const config = ConfigSystem.loadFromEnvironment();
      
      expect(ConfigSystem.getValue(config, 'agent.name')).toBe('ai-teammate');
      expect(ConfigSystem.getValue(config, 'integrations.storage.type')).toBe('mock');
    });
  });

  describe('hasValue', () => {
    test('should check if value exists', () => {
      const config = ConfigSystem.loadFromEnvironment();
      config.organization.name = 'test-org';
      
      expect(ConfigSystem.hasValue(config, 'agent.name')).toBe(true);
      expect(ConfigSystem.hasValue(config, 'organization.name')).toBe(true);
      expect(ConfigSystem.hasValue(config, 'nonexistent.path')).toBe(false);
    });
  });
});
