/**
 * Teammate Engine tests
 */

import { TeammateEngine } from '../teammate-engine.js';
import type { TeammateConfig, TeammatePlugin } from '../../shared/types.js';
import { BasePlugin } from '../../plugins/base-plugin.js';

// Create mock functions for testing
const mockFn = () => () => {};

// Mock plugin for testing
class MockPlugin extends BasePlugin {
  getName(): string {
    return 'mock-plugin';
  }

  getRole(): string {
    return 'Mock Assistant';
  }

  getPersonality() {
    return {
      avatar: '🤖',
      voice: 'professional',
      expertise: ['testing'],
      communicationStyle: 'professional' as const,
      defaultPrompts: []
    };
  }

  getTools() {
    return [
      this.createTool(
        'mock-tool',
        'A mock tool',
        { type: 'object', properties: {} },
        async () => ({ result: 'mock' })
      )
    ];
  }

  getResources() {
    return [
      this.createResource(
        'mock://resource',
        'Mock Resource',
        'A mock resource',
        'text/plain',
        async () => ({
          uri: 'mock://resource',
          mimeType: 'text/plain',
          text: 'mock content'
        })
      )
    ];
  }

  getPrompts() {
    return [
      this.createPrompt(
        'mock-prompt',
        'A mock prompt',
        'Hello {{name}}!',
        [
          {
            name: 'name',
            description: 'Name to greet',
            required: true,
            type: 'string'
          }
        ]
      )
    ];
  }
}

describe('TeammateEngine', () => {
  let mockPlugin: TeammatePlugin;
  let mockConfig: TeammateConfig;

  beforeEach(() => {
    mockConfig = {
      agent: {
        name: 'test-agent',
        role: 'Test Agent',
        version: '1.0.0',
        personality: {
          avatar: '🤖',
          voice: 'professional',
          expertise: ['testing'],
          communicationStyle: 'professional',
          defaultPrompts: []
        }
      },
      organization: {
        name: 'test-org',
        defaultRepo: 'test-repo',
        defaultReviewer: 'test-reviewer',
        defaultBranch: 'main'
      },
      integrations: {
        doppler: { token: '', project: '', environment: 'test' },
        storage: { type: 'mock' },
        github: {
          statusNames: {
            humanReview: ['Review'],
            inProgress: ['In Progress'],
            completed: ['Done']
          },
          commentSignature: 'Test Agent'
        }
      },
      transport: { type: 'stdio' },
      features: {
        resources: {
          uriPrefix: 'test-agent',
          conversationHistoryLimit: 50,
          enabledResources: ['memory']
        },
        tools: {
          namePrefix: 'test-agent',
          enabledTools: ['memory']
        },
        prompts: {
          enabledPrompts: ['memory'],
          templatePath: './prompts'
        }
      }
    };

    mockPlugin = new MockPlugin();
  });

  describe('configuration validation', () => {
    test('should have valid mock config structure', () => {
      expect(mockConfig.agent.name).toBe('test-agent');
      expect(mockConfig.agent.role).toBe('Test Agent');
      expect(mockConfig.transport.type).toBe('stdio');
      expect(mockConfig.features.resources.uriPrefix).toBe('test-agent');
    });

    test('should have valid organization config', () => {
      expect(mockConfig.organization.name).toBe('test-org');
      expect(mockConfig.organization.defaultRepo).toBe('test-repo');
      expect(mockConfig.organization.defaultBranch).toBe('main');
    });

    test('should have valid integrations config', () => {
      expect(mockConfig.integrations.storage.type).toBe('mock');
      expect(mockConfig.integrations.github.commentSignature).toBe('Test Agent');
      expect(mockConfig.integrations.doppler.environment).toBe('test');
    });
  });

  describe('plugin validation', () => {
    test('should have valid plugin interface', () => {
      expect(mockPlugin.getName()).toBe('mock-plugin');
      expect(mockPlugin.getRole()).toBe('Mock Assistant');
      expect(mockPlugin.getPersonality()).toBeDefined();
      expect(mockPlugin.getPersonality().communicationStyle).toBe('professional');
    });

    test('should have required plugin methods', () => {
      expect(typeof mockPlugin.initialize).toBe('function');
      expect(typeof mockPlugin.getName).toBe('function');
      expect(typeof mockPlugin.getRole).toBe('function');
      expect(typeof mockPlugin.getPersonality).toBe('function');
    });

    test('should handle plugin initialization', async () => {
      const mockLogger = {
        info: mockFn(),
        warn: mockFn(),
        error: mockFn(),
        debug: mockFn()
      };
      const mockContext = {
        config: mockConfig,
        uriBuilder: {} as any,
        logger: mockLogger as any,
        integrations: {} as any
      };
      await expect(mockPlugin.initialize(mockContext)).resolves.not.toThrow();
    });
  });

  describe('transport configuration', () => {
    test('should have valid stdio transport config', () => {
      expect(mockConfig.transport.type).toBe('stdio');
    });

    test('should support SSE transport config', () => {
      const sseConfig = { ...mockConfig, transport: { type: 'sse' as const } };
      expect(sseConfig.transport.type).toBe('sse');
    });

    test('should validate transport type', () => {
      const validTypes = ['stdio', 'sse'];
      expect(validTypes).toContain(mockConfig.transport.type);
    });
  });

  describe('features configuration', () => {
    test('should have valid resource features', () => {
      expect(mockConfig.features.resources.uriPrefix).toBe('test-agent');
      expect(mockConfig.features.resources.conversationHistoryLimit).toBe(50);
      expect(mockConfig.features.resources.enabledResources).toContain('memory');
    });

    test('should have valid tool features', () => {
      expect(mockConfig.features.tools.namePrefix).toBe('test-agent');
      expect(mockConfig.features.tools.enabledTools).toContain('memory');
    });

    test('should have valid prompt features', () => {
      expect(mockConfig.features.prompts.enabledPrompts).toContain('memory');
      expect(mockConfig.features.prompts.templatePath).toBe('./prompts');
    });
  });
});
