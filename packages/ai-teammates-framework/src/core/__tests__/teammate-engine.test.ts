/**
 * Teammate Engine tests
 */

import { TeammateEngine } from '../teammate-engine.js';
import type { TeammateConfig, TeammatePlugin } from '../../shared/types.js';
import { BasePlugin } from '../../plugins/base-plugin.js';

// Mock plugin for testing
class MockPlugin extends BasePlugin {
  getName(): string {
    return 'mock-plugin';
  }

  getRole(): string {
    return 'Mock Assistant';
  }

  getPersonality() {
    return {
      avatar: '🤖',
      voice: 'professional',
      expertise: ['testing'],
      communicationStyle: 'professional' as const,
      defaultPrompts: []
    };
  }

  getTools() {
    return [
      this.createTool(
        'mock-tool',
        'A mock tool',
        { type: 'object', properties: {} },
        async () => ({ result: 'mock' })
      )
    ];
  }

  getResources() {
    return [
      this.createResource(
        'mock://resource',
        'Mock Resource',
        'A mock resource',
        'text/plain',
        async () => ({
          uri: 'mock://resource',
          mimeType: 'text/plain',
          text: 'mock content'
        })
      )
    ];
  }

  getPrompts() {
    return [
      this.createPrompt(
        'mock-prompt',
        'A mock prompt',
        'Hello {{name}}!',
        [
          {
            name: 'name',
            description: 'Name to greet',
            required: true,
            type: 'string'
          }
        ]
      )
    ];
  }
}

describe.skip('TeammateEngine', () => {
  let engine: TeammateEngine;
  let mockPlugin: TeammatePlugin;
  let mockConfig: TeammateConfig;

  beforeEach(() => {
    mockConfig = {
      agent: {
        name: 'test-agent',
        role: 'Test Agent',
        version: '1.0.0',
        personality: {
          avatar: '🤖',
          voice: 'professional',
          expertise: ['testing'],
          communicationStyle: 'professional',
          defaultPrompts: []
        }
      },
      organization: {
        name: 'test-org',
        defaultRepo: 'test-repo',
        defaultReviewer: 'test-reviewer',
        defaultBranch: 'main'
      },
      integrations: {
        doppler: { token: '', project: '', environment: 'test' },
        storage: { type: 'mock' },
        github: {
          statusNames: {
            humanReview: ['Review'],
            inProgress: ['In Progress'],
            completed: ['Done']
          },
          commentSignature: 'Test Agent'
        }
      },
      transport: { type: 'stdio' },
      features: {
        resources: {
          uriPrefix: 'test-agent',
          conversationHistoryLimit: 50,
          enabledResources: ['memory']
        },
        tools: {
          namePrefix: 'test-agent',
          enabledTools: []
        },
        prompts: {
          enabledPrompts: [],
          templatePath: './prompts'
        }
      }
    };

    mockPlugin = new MockPlugin();
    engine = new TeammateEngine(mockConfig, mockPlugin);
  });

  describe('constructor', () => {
    test('should create engine with config and plugin', () => {
      expect(engine).toBeInstanceOf(TeammateEngine);
      expect(engine.getServer()).toBeDefined();
    });
  });

  describe('initialization', () => {
    test('should initialize successfully', async () => {
      await expect(engine.initialize()).resolves.not.toThrow();
    });

    test('should not initialize twice', async () => {
      await engine.initialize();
      
      // Should not throw but should warn
      await expect(engine.initialize()).resolves.not.toThrow();
    });

    test('should handle plugin initialization errors', async () => {
      const errorPlugin = {
        ...mockPlugin,
        initialize: async () => {
          throw new Error('Plugin init failed');
        }
      };
      
      const errorEngine = new TeammateEngine(mockConfig, errorPlugin);
      await expect(errorEngine.initialize()).rejects.toThrow('Failed to initialize teammate engine');
    });
  });

  describe('start and stop', () => {
    test('should require initialization before starting', async () => {
      await expect(engine.start()).rejects.toThrow('Engine must be initialized before starting');
    });

    test('should start and stop successfully', async () => {
      await engine.initialize();
      
      // Mock the server connect method to avoid actual connection
      const mockConnect = async () => Promise.resolve();
      const mockClose = async () => Promise.resolve();

      engine.getServer().connect = mockConnect;
      engine.getServer().close = mockClose;
      
      await expect(engine.start()).resolves.not.toThrow();
      await expect(engine.stop()).resolves.not.toThrow();
      
      // Test passes if no errors are thrown
    });

    test('should handle start errors', async () => {
      await engine.initialize();
      
      const mockConnect = async () => {
        throw new Error('Connection failed');
      };
      engine.getServer().connect = mockConnect;
      
      await expect(engine.start()).rejects.toThrow('Failed to start teammate engine');
    });

    test('should handle stop errors', async () => {
      await engine.initialize();
      
      const mockClose = async () => {
        throw new Error('Close failed');
      };
      engine.getServer().close = mockClose;
      
      await expect(engine.stop()).rejects.toThrow('Failed to stop teammate engine');
    });
  });

  describe('transport creation', () => {
    test('should create stdio transport by default', async () => {
      await engine.initialize();
      // Transport creation is tested indirectly through start()
    });

    test('should fall back to stdio for SSE transport', async () => {
      const sseConfig = { ...mockConfig, transport: { type: 'sse' as const } };
      const sseEngine = new TeammateEngine(sseConfig, mockPlugin);
      
      await sseEngine.initialize();
      // Should not throw and should log warning about SSE fallback
    });

    test('should throw for unsupported transport type', async () => {
      const invalidConfig = { ...mockConfig, transport: { type: 'invalid' as any } };
      const invalidEngine = new TeammateEngine(invalidConfig, mockPlugin);
      
      await invalidEngine.initialize();
      
      const mockConnect = async () => {
        throw new Error('Unsupported transport type: invalid');
      };
      invalidEngine.getServer().connect = mockConnect;
      
      await expect(invalidEngine.start()).rejects.toThrow('Failed to start teammate engine');
    });
  });

  describe('server access', () => {
    test('should provide access to MCP server', () => {
      const server = engine.getServer();
      expect(server).toBeDefined();
      expect(typeof server.setRequestHandler).toBe('function');
    });
  });
});
