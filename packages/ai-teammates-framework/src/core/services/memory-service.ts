/**
 * Memory Service
 * In-memory storage for agent state and context
 */

import type { MemoryService as IMemoryService } from '../../shared/types.js';
import { safeJsonStringify, safeJsonParse } from '../../shared/utils.js';

export class MemoryService implements IMemoryService {
  private memory = new Map<string, any>();

  /**
   * Set a value in memory
   */
  set(key: string, value: any): void {
    if (!key) {
      throw new Error('Memory key cannot be empty');
    }
    this.memory.set(key, value);
  }

  /**
   * Get a value from memory
   */
  get(key: string): any {
    if (!key) {
      throw new Error('Memory key cannot be empty');
    }
    return this.memory.get(key);
  }

  /**
   * Check if key exists in memory
   */
  has(key: string): boolean {
    if (!key) {
      return false;
    }
    return this.memory.has(key);
  }

  /**
   * Delete a key from memory
   */
  delete(key: string): boolean {
    if (!key) {
      return false;
    }
    return this.memory.delete(key);
  }

  /**
   * Clear all memory
   */
  clear(): void {
    this.memory.clear();
  }

  /**
   * Get all memory as object
   */
  getAll(): Record<string, any> {
    const result: Record<string, any> = {};
    for (const [key, value] of this.memory.entries()) {
      result[key] = value;
    }
    return result;
  }

  /**
   * Get memory size
   */
  size(): number {
    return this.memory.size;
  }

  /**
   * Get all keys
   */
  keys(): string[] {
    return Array.from(this.memory.keys());
  }

  /**
   * Get all values
   */
  values(): any[] {
    return Array.from(this.memory.values());
  }

  /**
   * Set multiple values at once
   */
  setMultiple(entries: Record<string, any>): void {
    for (const [key, value] of Object.entries(entries)) {
      this.set(key, value);
    }
  }

  /**
   * Get multiple values at once
   */
  getMultiple(keys: string[]): Record<string, any> {
    const result: Record<string, any> = {};
    for (const key of keys) {
      if (this.has(key)) {
        result[key] = this.get(key);
      }
    }
    return result;
  }

  /**
   * Delete multiple keys at once
   */
  deleteMultiple(keys: string[]): number {
    let deletedCount = 0;
    for (const key of keys) {
      if (this.delete(key)) {
        deletedCount++;
      }
    }
    return deletedCount;
  }

  /**
   * Serialize memory to JSON string
   */
  serialize(): string {
    return safeJsonStringify(this.getAll(), 2);
  }

  /**
   * Deserialize memory from JSON string
   */
  deserialize(json: string): void {
    const data = safeJsonParse(json, {});
    this.clear();
    this.setMultiple(data);
  }

  /**
   * Create a snapshot of current memory
   */
  snapshot(): Record<string, any> {
    return { ...this.getAll() };
  }

  /**
   * Restore memory from snapshot
   */
  restore(snapshot: Record<string, any>): void {
    this.clear();
    this.setMultiple(snapshot);
  }

  /**
   * Get memory usage statistics
   */
  getStats(): {
    size: number;
    keys: number;
    memoryUsage: string;
  } {
    const serialized = this.serialize();
    return {
      size: this.size(),
      keys: this.keys().length,
      memoryUsage: `${Math.round(serialized.length / 1024)} KB`
    };
  }

  /**
   * Find keys matching pattern
   */
  findKeys(pattern: string | RegExp): string[] {
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
    return this.keys().filter(key => regex.test(key));
  }

  /**
   * Get values for keys matching pattern
   */
  findValues(pattern: string | RegExp): Record<string, any> {
    const matchingKeys = this.findKeys(pattern);
    return this.getMultiple(matchingKeys);
  }

  /**
   * Increment numeric value (or set to 1 if doesn't exist)
   */
  increment(key: string, amount = 1): number {
    const current = this.get(key) || 0;
    const newValue = (typeof current === 'number' ? current : 0) + amount;
    this.set(key, newValue);
    return newValue;
  }

  /**
   * Decrement numeric value (or set to -amount if doesn't exist)
   */
  decrement(key: string, amount = 1): number {
    return this.increment(key, -amount);
  }

  /**
   * Append to array value (or create array if doesn't exist)
   */
  append(key: string, value: any): any[] {
    const current = this.get(key);
    const array = Array.isArray(current) ? current : [];
    array.push(value);
    this.set(key, array);
    return array;
  }

  /**
   * Prepend to array value (or create array if doesn't exist)
   */
  prepend(key: string, value: any): any[] {
    const current = this.get(key);
    const array = Array.isArray(current) ? current : [];
    array.unshift(value);
    this.set(key, array);
    return array;
  }
}
