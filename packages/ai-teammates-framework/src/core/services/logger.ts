/**
 * Logger Service
 * Structured logging for the AI Teammates Framework
 */

import type { Logger as ILogger } from '../../shared/types.js';
import { safeJsonStringify } from '../../shared/utils.js';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

export class Logger implements ILogger {
  private readonly agentName: string;
  private readonly logLevel: LogLevel;
  private readonly enableConsole: boolean;
  private readonly enableFile: boolean;
  private readonly logEntries: LogEntry[] = [];
  private readonly maxEntries: number;

  constructor(options: {
    agentName: string;
    logLevel?: LogLevel;
    enableConsole?: boolean;
    enableFile?: boolean;
    maxEntries?: number;
  }) {
    this.agentName = options.agentName;
    this.logLevel = options.logLevel || 'info';
    this.enableConsole = options.enableConsole !== false;
    this.enableFile = options.enableFile || false;
    this.maxEntries = options.maxEntries || 1000;
  }

  /**
   * Log info message
   */
  info(message: string, context?: Record<string, any>): void {
    this.log('info', message, context);
  }

  /**
   * Log warning message
   */
  warn(message: string, context?: Record<string, any>): void {
    this.log('warn', message, context);
  }

  /**
   * Log error message
   */
  error(message: string, error?: Error, context?: Record<string, any>): void {
    const errorInfo = error ? {
      name: error.name,
      message: error.message,
      stack: error.stack
    } : undefined;

    this.log('error', message, context, errorInfo);
  }

  /**
   * Log debug message
   */
  debug(message: string, context?: Record<string, any>): void {
    this.log('debug', message, context);
  }

  /**
   * Core logging method
   */
  private log(
    level: LogLevel, 
    message: string, 
    context?: Record<string, any>,
    error?: { name: string; message: string; stack?: string }
  ): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message: `[${this.agentName}] ${message}`,
      context,
      error
    };

    // Store in memory
    this.logEntries.push(entry);
    if (this.logEntries.length > this.maxEntries) {
      this.logEntries.shift();
    }

    // Console output
    if (this.enableConsole) {
      this.logToConsole(entry);
    }

    // File output (if enabled)
    if (this.enableFile) {
      this.logToFile(entry);
    }
  }

  /**
   * Check if should log at this level
   */
  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };

    return levels[level] >= levels[this.logLevel];
  }

  /**
   * Log to console
   */
  private logToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.substring(11, 19); // HH:MM:SS
    const prefix = `${timestamp} [${entry.level.toUpperCase()}]`;
    
    let output = `${prefix} ${entry.message}`;
    
    if (entry.context) {
      output += ` ${safeJsonStringify(entry.context)}`;
    }

    if (entry.error) {
      output += `\nError: ${entry.error.name}: ${entry.error.message}`;
      if (entry.error.stack) {
        output += `\n${entry.error.stack}`;
      }
    }

    switch (entry.level) {
      case 'debug':
        console.debug(output);
        break;
      case 'info':
        console.info(output);
        break;
      case 'warn':
        console.warn(output);
        break;
      case 'error':
        console.error(output);
        break;
    }
  }

  /**
   * Log to file (placeholder - would need file system implementation)
   */
  private logToFile(entry: LogEntry): void {
    // TODO: Implement file logging if needed
    // This would require fs module and proper file handling
  }

  /**
   * Get recent log entries
   */
  getRecentLogs(count = 50): LogEntry[] {
    return this.logEntries.slice(-count);
  }

  /**
   * Get logs by level
   */
  getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logEntries.filter(entry => entry.level === level);
  }

  /**
   * Get logs in time range
   */
  getLogsByTimeRange(startTime: Date, endTime: Date): LogEntry[] {
    return this.logEntries.filter(entry => {
      const entryTime = new Date(entry.timestamp);
      return entryTime >= startTime && entryTime <= endTime;
    });
  }

  /**
   * Search logs by message content
   */
  searchLogs(query: string): LogEntry[] {
    const lowerQuery = query.toLowerCase();
    return this.logEntries.filter(entry => 
      entry.message.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * Clear all log entries
   */
  clearLogs(): void {
    this.logEntries.length = 0;
  }

  /**
   * Get logging statistics
   */
  getStats(): {
    totalEntries: number;
    entriesByLevel: Record<LogLevel, number>;
    oldestEntry?: string;
    newestEntry?: string;
  } {
    const entriesByLevel: Record<LogLevel, number> = {
      debug: 0,
      info: 0,
      warn: 0,
      error: 0
    };

    for (const entry of this.logEntries) {
      entriesByLevel[entry.level]++;
    }

    return {
      totalEntries: this.logEntries.length,
      entriesByLevel,
      oldestEntry: this.logEntries[0]?.timestamp,
      newestEntry: this.logEntries[this.logEntries.length - 1]?.timestamp
    };
  }

  /**
   * Create child logger with additional context
   */
  child(context: Record<string, any>): Logger {
    const childLogger = new Logger({
      agentName: this.agentName,
      logLevel: this.logLevel,
      enableConsole: this.enableConsole,
      enableFile: this.enableFile,
      maxEntries: this.maxEntries
    });

    // Override log method to include parent context
    const originalLog = childLogger.log.bind(childLogger);
    childLogger.log = (level: LogLevel, message: string, childContext?: Record<string, any>, error?: any) => {
      const mergedContext = { ...context, ...childContext };
      originalLog(level, message, mergedContext, error);
    };

    return childLogger;
  }
}
