/**
 * Memory Service tests
 */

import { MemoryService } from '../memory-service.js';

describe('MemoryService', () => {
  let memory: MemoryService;

  beforeEach(() => {
    memory = new MemoryService();
  });

  describe('basic operations', () => {
    test('should set and get values', () => {
      memory.set('key1', 'value1');
      expect(memory.get('key1')).toBe('value1');
    });

    test('should return undefined for non-existent keys', () => {
      expect(memory.get('nonexistent')).toBeUndefined();
    });

    test('should check if key exists', () => {
      memory.set('key1', 'value1');
      expect(memory.has('key1')).toBe(true);
      expect(memory.has('nonexistent')).toBe(false);
    });

    test('should delete keys', () => {
      memory.set('key1', 'value1');
      expect(memory.delete('key1')).toBe(true);
      expect(memory.has('key1')).toBe(false);
      expect(memory.delete('nonexistent')).toBe(false);
    });

    test('should clear all memory', () => {
      memory.set('key1', 'value1');
      memory.set('key2', 'value2');
      memory.clear();
      expect(memory.size()).toBe(0);
    });

    test('should throw error for empty keys', () => {
      expect(() => memory.set('', 'value')).toThrow('Memory key cannot be empty');
      expect(() => memory.get('')).toThrow('Memory key cannot be empty');
    });
  });

  describe('bulk operations', () => {
    test('should set multiple values', () => {
      memory.setMultiple({ key1: 'value1', key2: 'value2' });
      expect(memory.get('key1')).toBe('value1');
      expect(memory.get('key2')).toBe('value2');
    });

    test('should get multiple values', () => {
      memory.set('key1', 'value1');
      memory.set('key2', 'value2');
      memory.set('key3', 'value3');
      
      const result = memory.getMultiple(['key1', 'key3', 'nonexistent']);
      expect(result).toEqual({ key1: 'value1', key3: 'value3' });
    });

    test('should delete multiple keys', () => {
      memory.set('key1', 'value1');
      memory.set('key2', 'value2');
      memory.set('key3', 'value3');
      
      const deletedCount = memory.deleteMultiple(['key1', 'key3', 'nonexistent']);
      expect(deletedCount).toBe(2);
      expect(memory.has('key1')).toBe(false);
      expect(memory.has('key2')).toBe(true);
      expect(memory.has('key3')).toBe(false);
    });
  });

  describe('serialization', () => {
    test('should serialize and deserialize', () => {
      memory.set('key1', 'value1');
      memory.set('key2', { nested: 'object' });
      
      const serialized = memory.serialize();
      expect(typeof serialized).toBe('string');
      
      const newMemory = new MemoryService();
      newMemory.deserialize(serialized);
      
      expect(newMemory.get('key1')).toBe('value1');
      expect(newMemory.get('key2')).toEqual({ nested: 'object' });
    });

    test('should handle invalid JSON in deserialize', () => {
      expect(() => memory.deserialize('invalid json')).not.toThrow();
      expect(memory.size()).toBe(0);
    });
  });

  describe('snapshots', () => {
    test('should create and restore snapshots', () => {
      memory.set('key1', 'value1');
      memory.set('key2', 'value2');
      
      const snapshot = memory.snapshot();
      
      memory.set('key3', 'value3');
      memory.delete('key1');
      
      memory.restore(snapshot);
      
      expect(memory.get('key1')).toBe('value1');
      expect(memory.get('key2')).toBe('value2');
      expect(memory.has('key3')).toBe(false);
    });
  });

  describe('utility methods', () => {
    test('should get all keys and values', () => {
      memory.set('key1', 'value1');
      memory.set('key2', 'value2');
      
      expect(memory.keys()).toEqual(['key1', 'key2']);
      expect(memory.values()).toEqual(['value1', 'value2']);
    });

    test('should get stats', () => {
      memory.set('key1', 'value1');
      memory.set('key2', 'value2');
      
      const stats = memory.getStats();
      expect(stats.size).toBe(2);
      expect(stats.keys).toBe(2);
      expect(typeof stats.memoryUsage).toBe('string');
    });

    test('should find keys by pattern', () => {
      memory.set('user:1', 'user1');
      memory.set('user:2', 'user2');
      memory.set('config:theme', 'dark');
      
      const userKeys = memory.findKeys(/^user:/);
      expect(userKeys).toEqual(['user:1', 'user:2']);
      
      const configKeys = memory.findKeys('config:');
      expect(configKeys).toEqual(['config:theme']);
    });

    test('should find values by pattern', () => {
      memory.set('user:1', 'user1');
      memory.set('user:2', 'user2');
      memory.set('config:theme', 'dark');
      
      const userValues = memory.findValues(/^user:/);
      expect(userValues).toEqual({ 'user:1': 'user1', 'user:2': 'user2' });
    });
  });

  describe('numeric operations', () => {
    test('should increment values', () => {
      expect(memory.increment('counter')).toBe(1);
      expect(memory.increment('counter')).toBe(2);
      expect(memory.increment('counter', 5)).toBe(7);
    });

    test('should decrement values', () => {
      memory.set('counter', 10);
      expect(memory.decrement('counter')).toBe(9);
      expect(memory.decrement('counter', 3)).toBe(6);
    });

    test('should handle non-numeric values in increment/decrement', () => {
      memory.set('text', 'hello');
      expect(memory.increment('text')).toBe(1);
    });
  });

  describe('array operations', () => {
    test('should append to arrays', () => {
      memory.append('list', 'item1');
      memory.append('list', 'item2');
      expect(memory.get('list')).toEqual(['item1', 'item2']);
    });

    test('should prepend to arrays', () => {
      memory.append('list', 'item2');
      memory.prepend('list', 'item1');
      expect(memory.get('list')).toEqual(['item1', 'item2']);
    });

    test('should handle non-array values in append/prepend', () => {
      memory.set('notArray', 'value');
      memory.append('notArray', 'item');
      expect(memory.get('notArray')).toEqual(['item']);
    });
  });

  describe('error handling', () => {
    test('should handle serialization errors gracefully', () => {
      const circularObj: any = {};
      circularObj.self = circularObj;

      expect(() => memory.set('circular', circularObj)).not.toThrow();
      expect(memory.get('circular')).toBeUndefined();
    });

    test('should handle deserialization errors gracefully', () => {
      // Manually corrupt the stored data
      (memory as any).data.set('corrupted', 'invalid json');

      expect(memory.get('corrupted')).toBeUndefined();
    });

    test('should handle edge cases in serialization', () => {
      // Test with undefined values
      memory.set('undefined', undefined);
      expect(memory.get('undefined')).toBeUndefined();

      // Test with null values
      memory.set('null', null);
      expect(memory.get('null')).toBeNull();
    });
  });
});
