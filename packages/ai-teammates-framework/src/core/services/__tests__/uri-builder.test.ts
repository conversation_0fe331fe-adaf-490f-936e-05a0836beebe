/**
 * URI Builder tests
 */

import { URIBuilder } from '../uri-builder.js';

describe('URIBuilder', () => {
  let uriBuilder: URIBuilder;

  beforeEach(() => {
    uriBuilder = new URIBuilder('test-agent');
  });

  describe('constructor', () => {
    test('should create URI builder with prefix', () => {
      expect(uriBuilder.getPrefix()).toBe('test-agent');
    });

    test('should convert prefix to lowercase', () => {
      const builder = new URIBuilder('TEST-AGENT');
      expect(builder.getPrefix()).toBe('test-agent');
    });

    test('should throw error for empty prefix', () => {
      expect(() => new URIBuilder('')).toThrow('URI prefix is required');
    });
  });

  describe('memory', () => {
    test('should generate memory URI', () => {
      expect(uriBuilder.memory('project')).toBe('memory://test-agent/project');
    });

    test('should throw error for empty resource', () => {
      expect(() => uriBuilder.memory('')).toThrow('Resource name is required');
    });
  });

  describe('template', () => {
    test('should generate template URI', () => {
      expect(uriBuilder.template('business-case')).toBe('template://test-agent/business-case');
    });

    test('should throw error for empty name', () => {
      expect(() => uriBuilder.template('')).toThrow('Template name is required');
    });
  });

  describe('document', () => {
    test('should generate document URI', () => {
      expect(uriBuilder.document('report.md')).toBe('file://test-agent/documents/report.md');
    });

    test('should throw error for empty path', () => {
      expect(() => uriBuilder.document('')).toThrow('Document path is required');
    });
  });

  describe('conversations', () => {
    test('should generate conversations URI', () => {
      expect(uriBuilder.conversations()).toBe('memory://test-agent/conversations');
    });
  });

  describe('currentProject', () => {
    test('should generate current project URI', () => {
      expect(uriBuilder.currentProject()).toBe('memory://test-agent/current-project');
    });
  });

  describe('isOwnURI', () => {
    test('should identify own URIs', () => {
      expect(uriBuilder.isOwnURI('memory://test-agent/project')).toBe(true);
      expect(uriBuilder.isOwnURI('template://test-agent/template')).toBe(true);
      expect(uriBuilder.isOwnURI('file://test-agent/documents/doc.md')).toBe(true);
    });

    test('should reject other agent URIs', () => {
      expect(uriBuilder.isOwnURI('memory://other-agent/project')).toBe(false);
      expect(uriBuilder.isOwnURI('template://other-agent/template')).toBe(false);
    });
  });

  describe('extractMemoryResource', () => {
    test('should extract memory resource', () => {
      expect(uriBuilder.extractMemoryResource('memory://test-agent/project')).toBe('project');
    });

    test('should return null for non-memory URI', () => {
      expect(uriBuilder.extractMemoryResource('template://test-agent/template')).toBeNull();
    });

    test('should return null for other agent URI', () => {
      expect(uriBuilder.extractMemoryResource('memory://other-agent/project')).toBeNull();
    });
  });

  describe('extractTemplateName', () => {
    test('should extract template name', () => {
      expect(uriBuilder.extractTemplateName('template://test-agent/business-case')).toBe('business-case');
    });

    test('should return null for non-template URI', () => {
      expect(uriBuilder.extractTemplateName('memory://test-agent/project')).toBeNull();
    });
  });

  describe('extractDocumentPath', () => {
    test('should extract document path', () => {
      expect(uriBuilder.extractDocumentPath('file://test-agent/documents/report.md')).toBe('report.md');
    });

    test('should return null for non-document URI', () => {
      expect(uriBuilder.extractDocumentPath('memory://test-agent/project')).toBeNull();
    });
  });

  describe('parseURI', () => {
    test('should parse memory URI', () => {
      const result = uriBuilder.parseURI('memory://test-agent/project');
      expect(result).toEqual({ type: 'memory', identifier: 'project' });
    });

    test('should parse template URI', () => {
      const result = uriBuilder.parseURI('template://test-agent/business-case');
      expect(result).toEqual({ type: 'template', identifier: 'business-case' });
    });

    test('should parse document URI', () => {
      const result = uriBuilder.parseURI('file://test-agent/documents/report.md');
      expect(result).toEqual({ type: 'document', identifier: 'report.md' });
    });

    test('should return null for other agent URI', () => {
      const result = uriBuilder.parseURI('memory://other-agent/project');
      expect(result).toBeNull();
    });

    test('should return null for unknown scheme', () => {
      const result = uriBuilder.parseURI('unknown://test-agent/resource');
      expect(result).toBeNull();
    });

    test('should handle malformed URIs', () => {
      // Test malformed memory URIs that fail extraction
      expect(uriBuilder.parseURI('memory://')).toBeNull();
      expect(uriBuilder.parseURI('memory:///')).toBeNull();

      // Test malformed template URIs that fail extraction
      expect(uriBuilder.parseURI('template://')).toBeNull();
      expect(uriBuilder.parseURI('template:///')).toBeNull();

      // Test malformed file URIs that fail extraction
      expect(uriBuilder.parseURI('file://')).toBeNull();
      expect(uriBuilder.parseURI('file:///')).toBeNull();
    });
  });

  describe('custom', () => {
    test('should generate custom URI', () => {
      expect(uriBuilder.custom('custom', 'resource')).toBe('custom://test-agent/resource');
    });

    test('should throw error for empty scheme', () => {
      expect(() => uriBuilder.custom('', 'resource')).toThrow('Scheme is required');
    });

    test('should throw error for empty resource', () => {
      expect(() => uriBuilder.custom('custom', '')).toThrow('Resource is required');
    });
  });
});
