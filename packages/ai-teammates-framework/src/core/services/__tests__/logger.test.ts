/**
 * Logger tests
 */

import { Logger } from '../logger.js';

describe('Logger', () => {
  let logger: Logger;
  let consoleSpy: jest.SpyInstance;

  beforeEach(() => {
    logger = new Logger({
      agentName: 'test-agent',
      logLevel: 'debug',
      enableConsole: true,
      enableFile: false,
      maxEntries: 100
    });
    
    // Spy on console methods
    consoleSpy = jest.spyOn(console, 'info').mockImplementation();
    jest.spyOn(console, 'warn').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    jest.spyOn(console, 'debug').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('basic logging', () => {
    test('should log info messages', () => {
      logger.info('test message', { key: 'value' });
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[INFO] [test-agent] test message')
      );
    });

    test('should log warning messages', () => {
      const warnSpy = jest.spyOn(console, 'warn');
      logger.warn('warning message');
      
      expect(warnSpy).toHaveBeenCalledWith(
        expect.stringContaining('[WARN] [test-agent] warning message')
      );
    });

    test('should log error messages', () => {
      const errorSpy = jest.spyOn(console, 'error');
      const error = new Error('test error');
      logger.error('error message', error);
      
      expect(errorSpy).toHaveBeenCalledWith(
        expect.stringContaining('[ERROR] [test-agent] error message')
      );
    });

    test('should log debug messages', () => {
      const debugSpy = jest.spyOn(console, 'debug');
      logger.debug('debug message');
      
      expect(debugSpy).toHaveBeenCalledWith(
        expect.stringContaining('[DEBUG] [test-agent] debug message')
      );
    });
  });

  describe('log levels', () => {
    test('should respect log level filtering', () => {
      const infoLogger = new Logger({
        agentName: 'test',
        logLevel: 'info',
        enableConsole: true
      });
      
      const debugSpy = jest.spyOn(console, 'debug');
      infoLogger.debug('should not appear');
      
      expect(debugSpy).not.toHaveBeenCalled();
    });

    test('should log messages at or above log level', () => {
      const warnLogger = new Logger({
        agentName: 'test',
        logLevel: 'warn',
        enableConsole: true
      });
      
      const infoSpy = jest.spyOn(console, 'info');
      const warnSpy = jest.spyOn(console, 'warn');
      
      warnLogger.info('should not appear');
      warnLogger.warn('should appear');
      
      expect(infoSpy).not.toHaveBeenCalled();
      expect(warnSpy).toHaveBeenCalled();
    });
  });

  describe('memory storage', () => {
    test('should store log entries in memory', () => {
      logger.info('test message 1');
      logger.warn('test message 2');
      
      const logs = logger.getRecentLogs(10);
      expect(logs).toHaveLength(2);
      expect(logs[0].message).toContain('test message 1');
      expect(logs[1].message).toContain('test message 2');
    });

    test('should limit stored entries', () => {
      const smallLogger = new Logger({
        agentName: 'test',
        maxEntries: 2
      });
      
      smallLogger.info('message 1');
      smallLogger.info('message 2');
      smallLogger.info('message 3');
      
      const logs = smallLogger.getRecentLogs(10);
      expect(logs).toHaveLength(2);
      expect(logs[0].message).toContain('message 2');
      expect(logs[1].message).toContain('message 3');
    });
  });

  describe('log retrieval', () => {
    beforeEach(() => {
      logger.info('info message');
      logger.warn('warn message');
      logger.error('error message');
      logger.debug('debug message');
    });

    test('should get logs by level', () => {
      const warnLogs = logger.getLogsByLevel('warn');
      expect(warnLogs).toHaveLength(1);
      expect(warnLogs[0].level).toBe('warn');
    });

    test('should get logs by time range', () => {
      const start = new Date(Date.now() - 1000);
      const end = new Date(Date.now() + 1000);
      
      const logs = logger.getLogsByTimeRange(start, end);
      expect(logs.length).toBeGreaterThan(0);
    });

    test('should search logs by message content', () => {
      const logs = logger.searchLogs('info');
      expect(logs).toHaveLength(1);
      expect(logs[0].message).toContain('info message');
    });
  });

  describe('utility methods', () => {
    test('should clear logs', () => {
      logger.info('test');
      logger.clearLogs();
      
      const logs = logger.getRecentLogs(10);
      expect(logs).toHaveLength(0);
    });

    test('should get stats', () => {
      logger.info('info');
      logger.warn('warn');
      logger.error('error');
      
      const stats = logger.getStats();
      expect(stats.totalEntries).toBe(3);
      expect(stats.entriesByLevel.info).toBe(1);
      expect(stats.entriesByLevel.warn).toBe(1);
      expect(stats.entriesByLevel.error).toBe(1);
    });

    test('should create child logger', () => {
      const child = logger.child({ component: 'test' });
      expect(child).toBeInstanceOf(Logger);
    });
  });

  describe('console disabled', () => {
    test('should not log to console when disabled', () => {
      const silentLogger = new Logger({
        agentName: 'test',
        enableConsole: false
      });
      
      const infoSpy = jest.spyOn(console, 'info');
      silentLogger.info('should not appear in console');
      
      expect(infoSpy).not.toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    test('should handle error objects properly', () => {
      const error = new Error('test error');
      error.stack = 'test stack trace';
      
      logger.error('error occurred', error);
      
      const logs = logger.getRecentLogs(1);
      expect(logs[0].error).toEqual({
        name: 'Error',
        message: 'test error',
        stack: 'test stack trace'
      });
    });

    test('should handle missing error stack', () => {
      const error = new Error('test error');
      delete error.stack;
      
      logger.error('error occurred', error);
      
      const logs = logger.getRecentLogs(1);
      expect(logs[0].error?.stack).toBeUndefined();
    });
  });
});
