/**
 * Logger tests
 */

import { Logger } from '../logger.js';

describe('Logger', () => {
  let logger: Logger;
  let originalConsole: any;

  beforeEach(() => {
    // Mock console methods
    originalConsole = {
      info: console.info,
      warn: console.warn,
      error: console.error,
      debug: console.debug
    };

    console.info = () => {};
    console.warn = () => {};
    console.error = () => {};
    console.debug = () => {};

    logger = new Logger({
      agentName: 'test-agent',
      logLevel: 'debug',
      enableConsole: true,
      enableFile: false,
      maxEntries: 100
    });
  });

  afterEach(() => {
    // Restore console methods
    console.info = originalConsole.info;
    console.warn = originalConsole.warn;
    console.error = originalConsole.error;
    console.debug = originalConsole.debug;
  });

  describe('basic logging', () => {
    test('should log info messages', () => {
      logger.info('test message', { key: 'value' });

      const logs = logger.getRecentLogs(1);
      expect(logs).toHaveLength(1);
      expect(logs[0].level).toBe('info');
      expect(logs[0].message).toContain('[test-agent] test message');
      expect(logs[0].context).toEqual({ key: 'value' });
    });

    test('should log warning messages', () => {
      logger.warn('warning message');

      const logs = logger.getRecentLogs(1);
      expect(logs).toHaveLength(1);
      expect(logs[0].level).toBe('warn');
      expect(logs[0].message).toContain('[test-agent] warning message');
    });

    test('should log error messages', () => {
      const error = new Error('test error');
      logger.error('error message', error);

      const logs = logger.getRecentLogs(1);
      expect(logs).toHaveLength(1);
      expect(logs[0].level).toBe('error');
      expect(logs[0].message).toContain('[test-agent] error message');
      expect(logs[0].error).toEqual({
        name: 'Error',
        message: 'test error',
        stack: error.stack
      });
    });

    test('should log debug messages', () => {
      logger.debug('debug message');

      const logs = logger.getRecentLogs(1);
      expect(logs).toHaveLength(1);
      expect(logs[0].level).toBe('debug');
      expect(logs[0].message).toContain('[test-agent] debug message');
    });
  });

  describe('log levels', () => {
    test('should respect log level filtering', () => {
      const infoLogger = new Logger({
        agentName: 'test',
        logLevel: 'info',
        enableConsole: true
      });

      infoLogger.debug('should not appear');

      const logs = infoLogger.getRecentLogs(10);
      expect(logs).toHaveLength(0);
    });

    test('should log messages at or above log level', () => {
      const warnLogger = new Logger({
        agentName: 'test',
        logLevel: 'warn',
        enableConsole: true
      });

      warnLogger.info('should not appear');
      warnLogger.warn('should appear');

      const logs = warnLogger.getRecentLogs(10);
      expect(logs).toHaveLength(1);
      expect(logs[0].level).toBe('warn');
    });
  });

  describe('memory storage', () => {
    test('should store log entries in memory', () => {
      logger.info('test message 1');
      logger.warn('test message 2');
      
      const logs = logger.getRecentLogs(10);
      expect(logs).toHaveLength(2);
      expect(logs[0].message).toContain('test message 1');
      expect(logs[1].message).toContain('test message 2');
    });

    test('should limit stored entries', () => {
      const smallLogger = new Logger({
        agentName: 'test',
        maxEntries: 2
      });
      
      smallLogger.info('message 1');
      smallLogger.info('message 2');
      smallLogger.info('message 3');
      
      const logs = smallLogger.getRecentLogs(10);
      expect(logs).toHaveLength(2);
      expect(logs[0].message).toContain('message 2');
      expect(logs[1].message).toContain('message 3');
    });
  });

  describe('log retrieval', () => {
    beforeEach(() => {
      logger.info('info message');
      logger.warn('warn message');
      logger.error('error message');
      logger.debug('debug message');
    });

    test('should get logs by level', () => {
      const warnLogs = logger.getLogsByLevel('warn');
      expect(warnLogs).toHaveLength(1);
      expect(warnLogs[0].level).toBe('warn');
    });

    test('should get logs by time range', () => {
      const start = new Date(Date.now() - 1000);
      const end = new Date(Date.now() + 1000);
      
      const logs = logger.getLogsByTimeRange(start, end);
      expect(logs.length).toBeGreaterThan(0);
    });

    test('should search logs by message content', () => {
      const logs = logger.searchLogs('info');
      expect(logs).toHaveLength(1);
      expect(logs[0].message).toContain('info message');
    });
  });

  describe('utility methods', () => {
    test('should clear logs', () => {
      logger.info('test');
      logger.clearLogs();
      
      const logs = logger.getRecentLogs(10);
      expect(logs).toHaveLength(0);
    });

    test('should get stats', () => {
      logger.info('info');
      logger.warn('warn');
      logger.error('error');
      
      const stats = logger.getStats();
      expect(stats.totalEntries).toBe(3);
      expect(stats.entriesByLevel.info).toBe(1);
      expect(stats.entriesByLevel.warn).toBe(1);
      expect(stats.entriesByLevel.error).toBe(1);
    });

    test('should create child logger', () => {
      const child = logger.child({ component: 'test' });
      expect(child).toBeInstanceOf(Logger);
    });
  });

  describe('console disabled', () => {
    test('should not log to console when disabled', () => {
      const silentLogger = new Logger({
        agentName: 'test',
        enableConsole: false
      });

      silentLogger.info('should not appear in console');

      // Should still store in memory
      const logs = silentLogger.getRecentLogs(1);
      expect(logs).toHaveLength(1);
      expect(logs[0].message).toContain('should not appear in console');
    });
  });

  describe('error handling', () => {
    test('should handle error objects properly', () => {
      const error = new Error('test error');
      error.stack = 'test stack trace';
      
      logger.error('error occurred', error);
      
      const logs = logger.getRecentLogs(1);
      expect(logs[0].error).toEqual({
        name: 'Error',
        message: 'test error',
        stack: 'test stack trace'
      });
    });

    test('should handle missing error stack', () => {
      const error = new Error('test error');
      delete error.stack;

      logger.error('error occurred', error);

      const logs = logger.getRecentLogs(1);
      expect(logs[0].error?.stack).toBeUndefined();
    });

    test('should handle file logging errors gracefully', () => {
      const fileLogger = new Logger({
        agentName: 'test-agent',
        enableFile: true
      });

      // Should not throw even if file writing fails
      expect(() => fileLogger.info('test message')).not.toThrow();
    });

    test('should handle log formatting errors', () => {
      const circularObj: any = {};
      circularObj.self = circularObj;

      // Should not throw even with circular references
      expect(() => logger.info('test', circularObj)).not.toThrow();
    });
  });
});
