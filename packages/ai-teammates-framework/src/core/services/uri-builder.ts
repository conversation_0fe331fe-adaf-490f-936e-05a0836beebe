/**
 * URI Builder Service
 * Generates dynamic URIs based on agent configuration
 */

import type { URIBuilder as IURIBuilder } from '../../shared/types.js';

export class URIBuilder implements IURIBuilder {
  private readonly prefix: string;

  constructor(prefix: string) {
    if (!prefix) {
      throw new Error('URI prefix is required');
    }
    this.prefix = prefix.toLowerCase();
  }

  /**
   * Generate memory URI
   */
  memory(resource: string): string {
    if (!resource) {
      throw new Error('Resource name is required for memory URI');
    }
    return `memory://${this.prefix}/${resource}`;
  }

  /**
   * Generate template URI
   */
  template(name: string): string {
    if (!name) {
      throw new Error('Template name is required for template URI');
    }
    return `template://${this.prefix}/${name}`;
  }

  /**
   * Generate document URI
   */
  document(path: string): string {
    if (!path) {
      throw new Error('Document path is required for document URI');
    }
    return `file://${this.prefix}/documents/${path}`;
  }

  /**
   * Generate conversation URI
   */
  conversations(): string {
    return this.memory('conversations');
  }

  /**
   * Generate current project URI
   */
  currentProject(): string {
    return this.memory('current-project');
  }

  /**
   * Get the URI prefix
   */
  getPrefix(): string {
    return this.prefix;
  }

  /**
   * Check if URI belongs to this agent
   */
  isOwnURI(uri: string): boolean {
    return uri.includes(`://${this.prefix}/`) || uri.includes(`://${this.prefix}`);
  }

  /**
   * Extract resource name from memory URI
   */
  extractMemoryResource(uri: string): string | null {
    const memoryPrefix = `memory://${this.prefix}/`;
    if (uri.startsWith(memoryPrefix)) {
      return uri.substring(memoryPrefix.length);
    }
    return null;
  }

  /**
   * Extract template name from template URI
   */
  extractTemplateName(uri: string): string | null {
    const templatePrefix = `template://${this.prefix}/`;
    if (uri.startsWith(templatePrefix)) {
      return uri.substring(templatePrefix.length);
    }
    return null;
  }

  /**
   * Extract document path from document URI
   */
  extractDocumentPath(uri: string): string | null {
    const documentPrefix = `file://${this.prefix}/documents/`;
    if (uri.startsWith(documentPrefix)) {
      return uri.substring(documentPrefix.length);
    }
    return null;
  }

  /**
   * Parse URI and return type and identifier
   */
  parseURI(uri: string): { type: string; identifier: string } | null {
    if (!this.isOwnURI(uri)) {
      return null;
    }

    if (uri.startsWith('memory://')) {
      const resource = this.extractMemoryResource(uri);
      return resource ? { type: 'memory', identifier: resource } : null;
    }

    if (uri.startsWith('template://')) {
      const template = this.extractTemplateName(uri);
      return template ? { type: 'template', identifier: template } : null;
    }

    if (uri.startsWith('file://')) {
      const document = this.extractDocumentPath(uri);
      return document ? { type: 'document', identifier: document } : null;
    }

    return null;
  }

  /**
   * Generate custom URI with specified scheme
   */
  custom(scheme: string, resource: string): string {
    if (!scheme) {
      throw new Error('Scheme is required for custom URI');
    }
    if (!resource) {
      throw new Error('Resource is required for custom URI');
    }
    return `${scheme}://${this.prefix}/${resource}`;
  }
}
