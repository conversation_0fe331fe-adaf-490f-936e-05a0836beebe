# Migration Guide: From OOP to Functional Architecture

This guide helps you migrate from the object-oriented AI Teammates Framework to the new functional architecture.

## Overview of Changes

### Key Differences

| Aspect | OOP Version | Functional Version |
|--------|-------------|-------------------|
| **Paradigm** | Object-oriented classes | Pure functional programming |
| **State Management** | Mutable class properties | Immutable data structures |
| **Error Handling** | Try-catch exceptions | Result<T> pattern |
| **Composition** | Inheritance & polymorphism | Function composition |
| **Testing** | Mock-heavy testing | Pure function testing |
| **Type Safety** | Partial TypeScript usage | Strict TypeScript, no `any` |

### Benefits of Migration

- **Better Testability**: 100% test coverage with isolated, pure functions
- **Improved Reliability**: Immutable data prevents state corruption
- **Enhanced Type Safety**: Comprehensive TypeScript types catch errors at compile time
- **Easier Debugging**: Pure functions are predictable and side-effect free
- **Better Performance**: Functional composition enables optimization
- **Simpler Mental Model**: No hidden state or complex inheritance hierarchies

## Migration Steps

### Step 1: Update Imports

**Before (OOP):**
```typescript
import { AITeammateFramework, Tool, Resource } from '@ai-teammates/framework';
```

**After (Functional):**
```typescript
import { 
  createFramework, 
  createTool, 
  createResource, 
  createPrompt,
  success,
  failure 
} from '@ai-teammates/framework/functional';
```

### Step 2: Framework Initialization

**Before (OOP):**
```typescript
const framework = new AITeammateFramework({
  agentName: 'my-agent',
  // ... other config
});

await framework.initialize();
```

**After (Functional):**
```typescript
import { parseEnvironmentConfig } from '@ai-teammates/framework/functional';

const configResult = parseEnvironmentConfig();
if (configResult.isSuccess) {
  const framework = createFramework(
    configResult.data,
    tools,
    resources,
    prompts
  );
}
```

### Step 3: Tool Creation

**Before (OOP):**
```typescript
class CalculatorTool extends Tool {
  name = 'calculator';
  description = 'Perform calculations';
  
  async execute(args: any): Promise<string> {
    const { operation, a, b } = args;
    switch (operation) {
      case 'add':
        return `${a + b}`;
      default:
        throw new Error('Unknown operation');
    }
  }
}

framework.addTool(new CalculatorTool());
```

**After (Functional):**
```typescript
const calculatorTool = createTool(
  'calculator',
  'Perform calculations',
  {
    type: 'object',
    properties: {
      operation: { type: 'string', enum: ['add', 'subtract'] },
      a: { type: 'number' },
      b: { type: 'number' }
    },
    required: ['operation', 'a', 'b']
  },
  async (args) => {
    const { operation, a, b } = args;
    switch (operation) {
      case 'add':
        return success(`${a + b}`);
      default:
        return failure('Unknown operation');
    }
  }
);

// Include in framework creation
const framework = createFramework(config, [calculatorTool]);
```

### Step 4: Resource Creation

**Before (OOP):**
```typescript
class UserResource extends Resource {
  uri = 'app://users/{id}';
  name = 'User Data';
  
  async read(uri: string): Promise<string> {
    const id = this.extractId(uri);
    const user = await this.fetchUser(id);
    return JSON.stringify(user);
  }
}

framework.addResource(new UserResource());
```

**After (Functional):**
```typescript
const userResource = createResource(
  'app://users/{id}',
  'User Data',
  'Access user information',
  'application/json',
  async (uri) => {
    try {
      const id = uri.split('/').pop();
      const user = await fetchUser(id);
      return success(JSON.stringify(user, null, 2));
    } catch (error) {
      return failure(`Failed to fetch user: ${error}`);
    }
  }
);

// Include in framework creation
const framework = createFramework(config, [], [userResource]);
```

### Step 5: Error Handling

**Before (OOP):**
```typescript
try {
  const result = await tool.execute(args);
  console.log(result);
} catch (error) {
  console.error('Tool failed:', error.message);
}
```

**After (Functional):**
```typescript
const result = await tool.handler(args);
if (result.isSuccess) {
  console.log(result.data);
} else {
  console.error('Tool failed:', result.error);
}
```

### Step 6: Memory Management

**Before (OOP):**
```typescript
framework.memory.set('key', 'value');
const value = framework.memory.get('key');
```

**After (Functional):**
```typescript
framework.memory.set('key', 'value');
const value = framework.memory.get('key');
// Same API, but now with immutable data structures
```

### Step 7: Logging

**Before (OOP):**
```typescript
framework.logger.info('Message', { context: 'data' });
```

**After (Functional):**
```typescript
framework.logger.info('Message', { context: 'data' });
// Same API, but now with pure functions
```

## Common Migration Patterns

### Pattern 1: Class to Factory Function

**Before:**
```typescript
class MyTool extends Tool {
  constructor(private config: Config) {
    super();
  }
  
  async execute(args: any) {
    // implementation
  }
}
```

**After:**
```typescript
const createMyTool = (config: Config) => createTool(
  'my-tool',
  'Description',
  schema,
  async (args) => {
    // implementation using config
    return success(result);
  }
);
```

### Pattern 2: Exception to Result

**Before:**
```typescript
async function operation() {
  if (error) {
    throw new Error('Something went wrong');
  }
  return result;
}
```

**After:**
```typescript
async function operation(): Promise<Result<string>> {
  if (error) {
    return failure('Something went wrong');
  }
  return success(result);
}
```

### Pattern 3: Mutable State to Immutable

**Before:**
```typescript
class Service {
  private state: any = {};
  
  updateState(key: string, value: any) {
    this.state[key] = value;
  }
}
```

**After:**
```typescript
// Use framework memory service or pass state as parameters
const updateState = (state: ReadonlyRecord<string, unknown>, key: string, value: unknown) => ({
  ...state,
  [key]: value
});
```

## Testing Migration

### Before (OOP):
```typescript
describe('CalculatorTool', () => {
  let tool: CalculatorTool;
  
  beforeEach(() => {
    tool = new CalculatorTool();
  });
  
  test('should add numbers', async () => {
    const result = await tool.execute({ operation: 'add', a: 2, b: 3 });
    expect(result).toBe('5');
  });
});
```

### After (Functional):
```typescript
describe('calculatorTool', () => {
  test('should add numbers', async () => {
    const tool = createCalculatorTool();
    const result = await tool.handler({ operation: 'add', a: 2, b: 3 });
    
    expect(result.isSuccess).toBe(true);
    if (result.isSuccess) {
      expect(result.data).toBe('5');
    }
  });
  
  test('should handle errors', async () => {
    const tool = createCalculatorTool();
    const result = await tool.handler({ operation: 'invalid', a: 2, b: 3 });
    
    expect(result.isFailure).toBe(true);
    if (result.isFailure) {
      expect(result.error).toContain('Unknown operation');
    }
  });
});
```

## Configuration Migration

### Environment Variables

The functional version uses environment variables for configuration:

```bash
# Agent Configuration
AGENT_NAME=my-ai-teammate
AGENT_ROLE=Development Assistant
AGENT_COMMUNICATION_STYLE=professional

# Transport
TRANSPORT_TYPE=stdio

# Features
ENABLED_TOOLS=memory,calculator
ENABLED_RESOURCES=memory,users
ENABLED_PROMPTS=conversation
```

### Configuration Object

**Before (OOP):**
```typescript
const config = {
  agentName: 'my-agent',
  tools: ['calculator'],
  // ... other settings
};
```

**After (Functional):**
```typescript
const configResult = parseEnvironmentConfig();
// Configuration is parsed from environment variables
```

## Troubleshooting

### Common Issues

1. **Type Errors**: The functional version has stricter typing
   - **Solution**: Remove `any` types, use proper TypeScript types

2. **Async/Await**: All handlers are async
   - **Solution**: Ensure all tool/resource/prompt handlers are async

3. **Error Handling**: No more try-catch, use Result pattern
   - **Solution**: Return `success(data)` or `failure(error)` from handlers

4. **Immutability**: Data structures are readonly
   - **Solution**: Use spread operator or immutable update patterns

### Migration Checklist

- [ ] Update imports to functional version
- [ ] Convert classes to factory functions
- [ ] Replace try-catch with Result pattern
- [ ] Update error handling to use `isSuccess`/`isFailure`
- [ ] Convert mutable state to immutable patterns
- [ ] Update tests to use functional patterns
- [ ] Configure environment variables
- [ ] Verify 100% test coverage
- [ ] Remove all `any` types
- [ ] Ensure all functions are pure

## Performance Considerations

### Memory Usage
- Functional version uses immutable data structures
- May use more memory initially but prevents memory leaks
- Garbage collection is more predictable

### Execution Speed
- Pure functions enable better optimization
- No object instantiation overhead
- Function composition can be optimized by bundlers

### Bundle Size
- Tree-shaking friendly exports
- No class inheritance overhead
- Smaller runtime footprint

## Support

If you encounter issues during migration:

1. Check the [Architecture Documentation](./ARCHITECTURE.md)
2. Review the [AI Knowledge Transfer](./AI_KNOWLEDGE_TRANSFER.md)
3. Examine the test files for usage examples
4. Open an issue on GitHub with migration questions

The functional architecture provides better reliability, testability, and maintainability while maintaining the same core functionality as the OOP version.
