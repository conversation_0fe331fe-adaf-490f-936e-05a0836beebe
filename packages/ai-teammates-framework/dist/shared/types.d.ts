/**
 * Core types for the AI Teammates Framework
 * Zero hardcoded values, fully configurable
 */
import type { Server } from '@modelcontextprotocol/sdk/server/index.js';
export interface TeammateConfig {
    agent: AgentConfig;
    organization: OrganizationConfig;
    integrations: IntegrationsConfig;
    transport: TransportConfig;
    features: FeaturesConfig;
}
export interface AgentConfig {
    name: string;
    role: string;
    version: string;
    personality: PersonalityConfig;
}
export interface PersonalityConfig {
    avatar: string;
    voice: string;
    expertise: string[];
    communicationStyle: 'professional' | 'casual' | 'technical' | 'friendly';
    defaultPrompts: string[];
}
export interface OrganizationConfig {
    name: string;
    defaultRepo: string;
    defaultReviewer: string;
    defaultBranch: string;
}
export interface IntegrationsConfig {
    doppler: DopplerConfig;
    storage: StorageConfig;
    github: GitHubConfig;
}
export interface DopplerConfig {
    token: string;
    project: string;
    environment: string;
}
export interface StorageConfig {
    type: 'mock' | 'file' | 'github';
    basePath?: string;
    github?: {
        owner: string;
        repo: string;
        branch: string;
    };
}
export interface GitHubConfig {
    statusNames: {
        humanReview: string[];
        inProgress: string[];
        completed: string[];
    };
    commentSignature: string;
}
export interface TransportConfig {
    type: 'stdio' | 'sse';
    options?: Record<string, any>;
}
export interface FeaturesConfig {
    resources: ResourcesConfig;
    tools: ToolsConfig;
    prompts: PromptsConfig;
}
export interface ResourcesConfig {
    uriPrefix: string;
    conversationHistoryLimit: number;
    enabledResources: string[];
}
export interface ToolsConfig {
    namePrefix: string;
    enabledTools: string[];
}
export interface PromptsConfig {
    enabledPrompts: string[];
    templatePath: string;
}
export interface TeammatePlugin {
    getName(): string;
    getRole(): string;
    getPersonality(): PersonalityConfig;
    getTools(): ToolDefinition[];
    getResources(): ResourceDefinition[];
    getPrompts(): PromptDefinition[];
    initialize(context: PluginContext): Promise<void>;
}
export interface PluginContext {
    config: TeammateConfig;
    uriBuilder: URIBuilder;
    logger: Logger;
    integrations: IntegrationServices;
}
export interface IntegrationServices {
    storage?: any;
    github?: any;
    doppler?: any;
}
export interface ToolDefinition {
    name: string;
    description: string;
    schema: ToolSchema;
    handler: ToolHandler;
}
export interface ToolSchema {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
}
export interface ToolHandler {
    (args: any, context: ExecutionContext): Promise<any>;
}
export interface ExecutionContext {
    config: TeammateConfig;
    uriBuilder: URIBuilder;
    logger: Logger;
    memory: MemoryService;
    storage: StorageService;
    integrations: IntegrationServices;
}
export interface ResourceDefinition {
    uri: string;
    name: string;
    description: string;
    mimeType: string;
    handler: ResourceHandler;
}
export interface ResourceHandler {
    (): Promise<ResourceContent>;
}
export interface ResourceContent {
    uri: string;
    mimeType: string;
    text: string;
}
export interface PromptDefinition {
    name: string;
    description: string;
    template: string;
    arguments: PromptArgument[];
}
export interface PromptArgument {
    name: string;
    description: string;
    required: boolean;
    type: 'string' | 'number' | 'boolean' | 'object';
}
export interface URIBuilder {
    memory(resource: string): string;
    template(name: string): string;
    document(path: string): string;
    conversations(): string;
    currentProject(): string;
}
export interface MemoryService {
    set(key: string, value: any): void;
    get(key: string): any;
    has(key: string): boolean;
    delete(key: string): boolean;
    clear(): void;
    getAll(): Record<string, any>;
}
export interface StorageService {
    save(path: string, content: string): Promise<{
        success: boolean;
        url?: string;
        error?: string;
    }>;
    read(path: string): Promise<{
        success: boolean;
        content?: string;
        error?: string;
    }>;
    list(): Promise<{
        success: boolean;
        documents?: DocumentInfo[];
        error?: string;
    }>;
}
export interface DocumentInfo {
    name: string;
    path: string;
    size?: number;
    lastModified?: Date;
}
export interface Logger {
    info(message: string, context?: Record<string, any>): void;
    warn(message: string, context?: Record<string, any>): void;
    error(message: string, error?: Error, context?: Record<string, any>): void;
    debug(message: string, context?: Record<string, any>): void;
}
export interface TeammateEngine {
    initialize(): Promise<void>;
    start(): Promise<void>;
    stop(): Promise<void>;
    getServer(): Server;
}
export interface FrameworkError extends Error {
    code: string;
    context?: Record<string, any>;
    retryable: boolean;
}
export interface ValidationResult {
    valid: boolean;
    errors: ValidationError[];
}
export interface ValidationError {
    path: string;
    message: string;
    value?: any;
}
//# sourceMappingURL=types.d.ts.map