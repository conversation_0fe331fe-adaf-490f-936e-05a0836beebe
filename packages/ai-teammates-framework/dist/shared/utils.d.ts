/**
 * Shared utilities for the AI Teammates Framework
 */
/**
 * Deep merge two objects
 */
export declare function deepMerge(target: Record<string, any>, source: Record<string, any>): Record<string, any>;
/**
 * Check if value is a plain object
 */
export declare function isObject(value: unknown): value is Record<string, any>;
/**
 * Get nested value from object using dot notation
 */
export declare function getNestedValue(obj: Record<string, any>, path: string): any;
/**
 * Set nested value in object using dot notation
 */
export declare function setNestedValue(obj: Record<string, any>, path: string, value: any): void;
/**
 * Check if value exists and is not null/undefined
 */
export declare function isDefined<T>(value: T | null | undefined): value is T;
/**
 * Ensure value is an array
 */
export declare function ensureArray<T>(value: T | T[]): T[];
/**
 * Sleep for specified milliseconds
 */
export declare function sleep(ms: number): Promise<void>;
/**
 * Retry function with exponential backoff
 */
export declare function retry<T>(fn: () => Promise<T>, options?: {
    maxAttempts?: number;
    baseDelay?: number;
    maxDelay?: number;
    backoffFactor?: number;
}): Promise<T>;
/**
 * Debounce function
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * Throttle function
 */
export declare function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void;
/**
 * Create a promise that resolves after a timeout
 */
export declare function timeout<T>(promise: Promise<T>, ms: number): Promise<T>;
/**
 * Safe JSON parse with fallback
 */
export declare function safeJsonParse<T>(json: string, fallback: T): T;
/**
 * Safe JSON stringify
 */
export declare function safeJsonStringify(value: any, space?: number): string;
/**
 * Generate a simple UUID v4
 */
export declare function generateUUID(): string;
/**
 * Sanitize string for use as filename
 */
export declare function sanitizeFilename(filename: string): string;
/**
 * Format bytes to human readable string
 */
export declare function formatBytes(bytes: number, decimals?: number): string;
//# sourceMappingURL=utils.d.ts.map