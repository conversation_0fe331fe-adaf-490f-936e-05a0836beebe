/**
 * Framework Error Classes
 * Structured error handling for the AI Teammates Framework
 */
export class BaseFrameworkError extends Error {
    code;
    context;
    retryable;
    constructor(code, message, retryable = false, context) {
        super(message);
        this.name = this.constructor.name;
        this.code = code;
        this.retryable = retryable;
        this.context = context;
        // Maintain proper stack trace for where our error was thrown
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, this.constructor);
        }
    }
}
export class ConfigurationError extends BaseFrameworkError {
    constructor(message, context) {
        super('CONFIGURATION_ERROR', message, false, context);
    }
}
export class PluginError extends BaseFrameworkError {
    constructor(message, retryable = false, context) {
        super('PLUGIN_ERROR', message, retryable, context);
    }
}
export class ToolExecutionError extends BaseFrameworkError {
    constructor(message, retryable = true, context) {
        super('TOOL_EXECUTION_ERROR', message, retryable, context);
    }
}
export class ResourceError extends BaseFrameworkError {
    constructor(message, retryable = true, context) {
        super('RESOURCE_ERROR', message, retryable, context);
    }
}
export class IntegrationError extends BaseFrameworkError {
    constructor(message, retryable = true, context) {
        super('INTEGRATION_ERROR', message, retryable, context);
    }
}
export class ValidationError extends BaseFrameworkError {
    constructor(message, context) {
        super('VALIDATION_ERROR', message, false, context);
    }
}
/**
 * Error factory for creating framework errors
 */
export class ErrorFactory {
    static configuration(message, context) {
        return new ConfigurationError(message, context);
    }
    static plugin(message, retryable = false, context) {
        return new PluginError(message, retryable, context);
    }
    static toolExecution(message, retryable = true, context) {
        return new ToolExecutionError(message, retryable, context);
    }
    static resource(message, retryable = true, context) {
        return new ResourceError(message, retryable, context);
    }
    static integration(message, retryable = true, context) {
        return new IntegrationError(message, retryable, context);
    }
    static validation(message, context) {
        return new ValidationError(message, context);
    }
}
/**
 * Error handler utility
 */
export class ErrorHandler {
    static isFrameworkError(error) {
        return error instanceof BaseFrameworkError;
    }
    static isRetryable(error) {
        return this.isFrameworkError(error) && error.retryable;
    }
    static getErrorCode(error) {
        if (this.isFrameworkError(error)) {
            return error.code;
        }
        return 'UNKNOWN_ERROR';
    }
    static getErrorContext(error) {
        if (this.isFrameworkError(error)) {
            return error.context;
        }
        return undefined;
    }
    static formatError(error) {
        if (this.isFrameworkError(error)) {
            const context = error.context ? ` (${JSON.stringify(error.context)})` : '';
            return `[${error.code}] ${error.message}${context}`;
        }
        if (error instanceof Error) {
            return error.message;
        }
        return String(error);
    }
}
//# sourceMappingURL=errors.js.map