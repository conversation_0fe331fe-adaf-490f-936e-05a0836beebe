/**
 * Framework Error Classes
 * Structured error handling for the AI Teammates Framework
 */
import type { FrameworkError } from './types.js';
export declare class BaseFrameworkError extends Error implements FrameworkError {
    readonly code: string;
    readonly context?: Record<string, any>;
    readonly retryable: boolean;
    constructor(code: string, message: string, retryable?: boolean, context?: Record<string, any>);
}
export declare class ConfigurationError extends BaseFrameworkError {
    constructor(message: string, context?: Record<string, any>);
}
export declare class PluginError extends BaseFrameworkError {
    constructor(message: string, retryable?: boolean, context?: Record<string, any>);
}
export declare class ToolExecutionError extends BaseFrameworkError {
    constructor(message: string, retryable?: boolean, context?: Record<string, any>);
}
export declare class ResourceError extends BaseFrameworkError {
    constructor(message: string, retryable?: boolean, context?: Record<string, any>);
}
export declare class IntegrationError extends BaseFrameworkError {
    constructor(message: string, retryable?: boolean, context?: Record<string, any>);
}
export declare class ValidationError extends BaseFrameworkError {
    constructor(message: string, context?: Record<string, any>);
}
/**
 * Error factory for creating framework errors
 */
export declare class ErrorFactory {
    static configuration(message: string, context?: Record<string, any>): ConfigurationError;
    static plugin(message: string, retryable?: boolean, context?: Record<string, any>): PluginError;
    static toolExecution(message: string, retryable?: boolean, context?: Record<string, any>): ToolExecutionError;
    static resource(message: string, retryable?: boolean, context?: Record<string, any>): ResourceError;
    static integration(message: string, retryable?: boolean, context?: Record<string, any>): IntegrationError;
    static validation(message: string, context?: Record<string, any>): ValidationError;
}
/**
 * Error handler utility
 */
export declare class ErrorHandler {
    static isFrameworkError(error: unknown): error is FrameworkError;
    static isRetryable(error: unknown): boolean;
    static getErrorCode(error: unknown): string;
    static getErrorContext(error: unknown): Record<string, any> | undefined;
    static formatError(error: unknown): string;
}
//# sourceMappingURL=errors.d.ts.map