{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/shared/errors.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAIH,MAAM,OAAO,kBAAmB,SAAQ,KAAK;IAC3B,IAAI,CAAS;IACb,OAAO,CAAuB;IAC9B,SAAS,CAAU;IAEnC,YACE,IAAY,EACZ,OAAe,EACf,SAAS,GAAG,KAAK,EACjB,OAA6B;QAE7B,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,6DAA6D;QAC7D,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC5B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;CACF;AAED,MAAM,OAAO,kBAAmB,SAAQ,kBAAkB;IACxD,YAAY,OAAe,EAAE,OAA6B;QACxD,KAAK,CAAC,qBAAqB,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;CACF;AAED,MAAM,OAAO,WAAY,SAAQ,kBAAkB;IACjD,YAAY,OAAe,EAAE,SAAS,GAAG,KAAK,EAAE,OAA6B;QAC3E,KAAK,CAAC,cAAc,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;CACF;AAED,MAAM,OAAO,kBAAmB,SAAQ,kBAAkB;IACxD,YAAY,OAAe,EAAE,SAAS,GAAG,IAAI,EAAE,OAA6B;QAC1E,KAAK,CAAC,sBAAsB,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;CACF;AAED,MAAM,OAAO,aAAc,SAAQ,kBAAkB;IACnD,YAAY,OAAe,EAAE,SAAS,GAAG,IAAI,EAAE,OAA6B;QAC1E,KAAK,CAAC,gBAAgB,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;CACF;AAED,MAAM,OAAO,gBAAiB,SAAQ,kBAAkB;IACtD,YAAY,OAAe,EAAE,SAAS,GAAG,IAAI,EAAE,OAA6B;QAC1E,KAAK,CAAC,mBAAmB,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC;CACF;AAED,MAAM,OAAO,eAAgB,SAAQ,kBAAkB;IACrD,YAAY,OAAe,EAAE,OAA6B;QACxD,KAAK,CAAC,kBAAkB,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,YAAY;IACvB,MAAM,CAAC,aAAa,CAAC,OAAe,EAAE,OAA6B;QACjE,OAAO,IAAI,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,OAAe,EAAE,SAAS,GAAG,KAAK,EAAE,OAA6B;QAC7E,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,OAAe,EAAE,SAAS,GAAG,IAAI,EAAE,OAA6B;QACnF,OAAO,IAAI,kBAAkB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,OAAe,EAAE,SAAS,GAAG,IAAI,EAAE,OAA6B;QAC9E,OAAO,IAAI,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAAe,EAAE,SAAS,GAAG,IAAI,EAAE,OAA6B;QACjF,OAAO,IAAI,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,OAAe,EAAE,OAA6B;QAC9D,OAAO,IAAI,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,YAAY;IACvB,MAAM,CAAC,gBAAgB,CAAC,KAAc;QACpC,OAAO,KAAK,YAAY,kBAAkB,CAAC;IAC7C,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,KAAc;QAC/B,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC;IACzD,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,KAAc;QAChC,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC,IAAI,CAAC;QACpB,CAAC;QACD,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,KAAc;QACnC,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC,OAAO,CAAC;QACvB,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,KAAc;QAC/B,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3E,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,GAAG,OAAO,EAAE,CAAC;QACtD,CAAC;QAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,OAAO,KAAK,CAAC,OAAO,CAAC;QACvB,CAAC;QAED,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;CACF"}