{"version": 3, "file": "config-system.js", "sourceRoot": "", "sources": ["../../src/core/config-system.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAOH,OAAO,EAAsB,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACvE,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAE/D,MAAM,OAAO,YAAY;IACvB;;OAEG;IACH,MAAM,CAAC,mBAAmB;QACxB,MAAM,MAAM,GAAmB;YAC7B,KAAK,EAAE;gBACL,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,aAAa;gBAC7C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,cAAc;gBAC9C,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,OAAO;gBAChF,WAAW,EAAE;oBACX,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,IAAI;oBACxC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,cAAc;oBAChD,SAAS,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBACnF,kBAAkB,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,cAAc,CAAQ;oBACpF,cAAc,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;iBACxG;aACF;YACD,YAAY,EAAE;gBACZ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE;gBAChC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;gBAC/C,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,EAAE;gBACvD,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,MAAM;aACxD;YACD,YAAY,EAAE;gBACZ,OAAO,EAAE;oBACP,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE;oBACtC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE;oBAC1C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,KAAK;iBAC9C;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,MAAM,CAAQ;oBACjD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,UAAU;oBACrD,MAAM,EAAE;wBACN,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE;wBAC7D,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;wBACnE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,MAAM;qBAC9E;iBACF;gBACD,MAAM,EAAE;oBACN,WAAW,EAAE;wBACX,WAAW,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,iDAAiD,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;wBACxI,UAAU,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,qBAAqB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;wBAC1G,SAAS,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,gBAAgB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;qBACnG;oBACD,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,0BAA0B;iBACrF;aACF;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,OAAO,CAAQ;gBACpD,OAAO,EAAE,IAAI,CAAC,qBAAqB,EAAE;aACtC;YACD,QAAQ,EAAE;gBACR,SAAS,EAAE;oBACT,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,IAAI,aAAa;oBACpG,wBAAwB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,IAAI,CAAC;oBAClF,gBAAgB,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,0CAA0C,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;iBAC9H;gBACD,KAAK,EAAE;oBACL,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,IAAI,aAAa;oBAClG,YAAY,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;iBAC9F;gBACD,OAAO,EAAE;oBACP,cAAc,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;oBACjG,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,WAAW;iBAC9D;aACF;SACF,CAAC;QAEF,kDAAkD;QAClD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,CAAC;YAC1C,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,gBAAgB,GAAG,gBAAgB,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAC3G,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB;QAClC,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;YAClC,IAAI,CAAC;gBACH,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,MAAsB;QACpC,MAAM,MAAM,GAAsB,EAAE,CAAC;QAErC,kBAAkB;QAClB,MAAM,cAAc,GAAG;YACrB,YAAY;YACZ,YAAY;YACZ,mBAAmB;SACpB,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAC5C,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,KAAK;oBACX,OAAO,EAAE,GAAG,KAAK,cAAc;oBAC/B,KAAK;iBACN,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACrE,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,gFAAgF;gBACzF,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;aACzB,CAAC,CAAC;QACL,CAAC;QAED,+BAA+B;QAC/B,MAAM,WAAW,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;QACxE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACvE,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,sCAAsC;gBAC5C,OAAO,EAAE,uCAAuC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACxE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,kBAAkB;aACnD,CAAC,CAAC;QACL,CAAC;QAED,wBAAwB;QACxB,MAAM,iBAAiB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAClE,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,2BAA2B;gBACjC,OAAO,EAAE,gCAAgC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACvE,KAAK,EAAE,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI;aACxC,CAAC,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,MAAM,mBAAmB,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YACzD,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,kCAAkC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC3E,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI;aAC7B,CAAC,CAAC;QACL,CAAC;QAED,sCAAsC;QACtC,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,wBAAwB,GAAG,CAAC,EAAE,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,6CAA6C;gBACnD,OAAO,EAAE,+CAA+C;gBACxD,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,wBAAwB;aAC1D,CAAC,CAAC;QACL,CAAC;QAED,4CAA4C;QAC5C,IAAI,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAClD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC;gBAC/C,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,mCAAmC;oBACzC,OAAO,EAAE,oDAAoD;oBAC7D,KAAK,EAAE,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK;iBACjD,CAAC,CAAC;YACL,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC;gBAC9C,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,kCAAkC;oBACxC,OAAO,EAAE,mDAAmD;oBAC5D,KAAK,EAAE,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI;iBAChD,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEzC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACtB,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvF,MAAM,YAAY,CAAC,aAAa,CAC9B,qCAAqC,aAAa,EAAE,EACpD,EAAE,MAAM,EAAE,UAAU,CAAC,MAAM,EAAE,CAC9B,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAI,MAAsB,EAAE,IAAY;QACrD,OAAO,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,MAAsB,EAAE,IAAY;QAClD,MAAM,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC3C,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;IAC1C,CAAC;CACF"}