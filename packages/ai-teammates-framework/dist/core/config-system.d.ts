/**
 * Configuration System
 * Zero hardcoded values, environment-driven configuration
 */
import type { TeammateConfig, ValidationResult } from '../shared/types.js';
export declare class ConfigSystem {
    /**
     * Load configuration from environment variables
     */
    static loadFromEnvironment(): TeammateConfig;
    /**
     * Parse transport options from environment
     */
    private static parseTransportOptions;
    /**
     * Validate configuration
     */
    static validate(config: TeammateConfig): ValidationResult;
    /**
     * Load and validate configuration
     */
    static loadAndValidate(): TeammateConfig;
    /**
     * Get configuration value by path
     */
    static getValue<T>(config: TeammateConfig, path: string): T;
    /**
     * Check if configuration has value at path
     */
    static hasValue(config: TeammateConfig, path: string): boolean;
}
//# sourceMappingURL=config-system.d.ts.map