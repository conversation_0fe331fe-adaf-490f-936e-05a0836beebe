/**
 * Teammate Engine
 * Core orchestrator for AI teammates with plugin architecture
 */
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import type { TeammateEngine as ITeammateEngine, TeammateConfig, TeammatePlugin } from '../shared/types.js';
export declare class TeammateEngine implements ITeammateEngine {
    private server;
    private plugin;
    private config;
    private uriBuilder;
    private memory;
    private logger;
    private integrations;
    private initialized;
    constructor(config: TeammateConfig, plugin: TeammatePlugin);
    /**
     * Initialize the teammate engine
     */
    initialize(): Promise<void>;
    /**
     * Start the teammate engine
     */
    start(): Promise<void>;
    /**
     * Stop the teammate engine
     */
    stop(): Promise<void>;
    /**
     * Get the MCP server instance
     */
    getServer(): Server;
    /**
     * Initialize integration services
     */
    private initializeIntegrations;
    /**
     * Setup tool handlers
     */
    private setupTools;
    /**
     * Setup resource handlers
     */
    private setupResources;
    /**
     * Setup prompt handlers
     */
    private setupPrompts;
    /**
     * Create transport based on configuration
     */
    private createTransport;
}
//# sourceMappingURL=teammate-engine.d.ts.map