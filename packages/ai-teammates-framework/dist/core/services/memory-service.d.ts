/**
 * Memory Service
 * In-memory storage for agent state and context
 */
import type { MemoryService as IMemoryService } from '../../shared/types.js';
export declare class MemoryService implements IMemoryService {
    private memory;
    /**
     * Set a value in memory
     */
    set(key: string, value: any): void;
    /**
     * Get a value from memory
     */
    get(key: string): any;
    /**
     * Check if key exists in memory
     */
    has(key: string): boolean;
    /**
     * Delete a key from memory
     */
    delete(key: string): boolean;
    /**
     * Clear all memory
     */
    clear(): void;
    /**
     * Get all memory as object
     */
    getAll(): Record<string, any>;
    /**
     * Get memory size
     */
    size(): number;
    /**
     * Get all keys
     */
    keys(): string[];
    /**
     * Get all values
     */
    values(): any[];
    /**
     * Set multiple values at once
     */
    setMultiple(entries: Record<string, any>): void;
    /**
     * Get multiple values at once
     */
    getMultiple(keys: string[]): Record<string, any>;
    /**
     * Delete multiple keys at once
     */
    deleteMultiple(keys: string[]): number;
    /**
     * Serialize memory to JSON string
     */
    serialize(): string;
    /**
     * Deserialize memory from JSON string
     */
    deserialize(json: string): void;
    /**
     * Create a snapshot of current memory
     */
    snapshot(): Record<string, any>;
    /**
     * Restore memory from snapshot
     */
    restore(snapshot: Record<string, any>): void;
    /**
     * Get memory usage statistics
     */
    getStats(): {
        size: number;
        keys: number;
        memoryUsage: string;
    };
    /**
     * Find keys matching pattern
     */
    findKeys(pattern: string | RegExp): string[];
    /**
     * Get values for keys matching pattern
     */
    findValues(pattern: string | RegExp): Record<string, any>;
    /**
     * Increment numeric value (or set to 1 if doesn't exist)
     */
    increment(key: string, amount?: number): number;
    /**
     * Decrement numeric value (or set to -amount if doesn't exist)
     */
    decrement(key: string, amount?: number): number;
    /**
     * Append to array value (or create array if doesn't exist)
     */
    append(key: string, value: any): any[];
    /**
     * Prepend to array value (or create array if doesn't exist)
     */
    prepend(key: string, value: any): any[];
}
//# sourceMappingURL=memory-service.d.ts.map