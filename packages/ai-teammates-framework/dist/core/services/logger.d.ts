/**
 * Logger Service
 * Structured logging for the AI Teammates Framework
 */
import type { Logger as ILogger } from '../../shared/types.js';
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
export interface LogEntry {
    timestamp: string;
    level: LogLevel;
    message: string;
    context?: Record<string, any>;
    error?: {
        name: string;
        message: string;
        stack?: string;
    };
}
export declare class Logger implements ILogger {
    private readonly agentName;
    private readonly logLevel;
    private readonly enableConsole;
    private readonly enableFile;
    private readonly logEntries;
    private readonly maxEntries;
    constructor(options: {
        agentName: string;
        logLevel?: LogLevel;
        enableConsole?: boolean;
        enableFile?: boolean;
        maxEntries?: number;
    });
    /**
     * Log info message
     */
    info(message: string, context?: Record<string, any>): void;
    /**
     * Log warning message
     */
    warn(message: string, context?: Record<string, any>): void;
    /**
     * Log error message
     */
    error(message: string, error?: Error, context?: Record<string, any>): void;
    /**
     * Log debug message
     */
    debug(message: string, context?: Record<string, any>): void;
    /**
     * Core logging method
     */
    private log;
    /**
     * Check if should log at this level
     */
    private shouldLog;
    /**
     * Log to console
     */
    private logToConsole;
    /**
     * Log to file (placeholder - would need file system implementation)
     */
    private logToFile;
    /**
     * Get recent log entries
     */
    getRecentLogs(count?: number): LogEntry[];
    /**
     * Get logs by level
     */
    getLogsByLevel(level: LogLevel): LogEntry[];
    /**
     * Get logs in time range
     */
    getLogsByTimeRange(startTime: Date, endTime: Date): LogEntry[];
    /**
     * Search logs by message content
     */
    searchLogs(query: string): LogEntry[];
    /**
     * Clear all log entries
     */
    clearLogs(): void;
    /**
     * Get logging statistics
     */
    getStats(): {
        totalEntries: number;
        entriesByLevel: Record<LogLevel, number>;
        oldestEntry?: string;
        newestEntry?: string;
    };
    /**
     * Create child logger with additional context
     */
    child(context: Record<string, any>): Logger;
}
//# sourceMappingURL=logger.d.ts.map