/**
 * URI Builder Service
 * Generates dynamic URIs based on agent configuration
 */
import type { URIBuilder as IURIBuilder } from '../../shared/types.js';
export declare class URIBuilder implements IURIBuilder {
    private readonly prefix;
    constructor(prefix: string);
    /**
     * Generate memory URI
     */
    memory(resource: string): string;
    /**
     * Generate template URI
     */
    template(name: string): string;
    /**
     * Generate document URI
     */
    document(path: string): string;
    /**
     * Generate conversation URI
     */
    conversations(): string;
    /**
     * Generate current project URI
     */
    currentProject(): string;
    /**
     * Get the URI prefix
     */
    getPrefix(): string;
    /**
     * Check if URI belongs to this agent
     */
    isOwnURI(uri: string): boolean;
    /**
     * Extract resource name from memory URI
     */
    extractMemoryResource(uri: string): string | null;
    /**
     * Extract template name from template URI
     */
    extractTemplateName(uri: string): string | null;
    /**
     * Extract document path from document URI
     */
    extractDocumentPath(uri: string): string | null;
    /**
     * Parse URI and return type and identifier
     */
    parseURI(uri: string): {
        type: string;
        identifier: string;
    } | null;
    /**
     * Generate custom URI with specified scheme
     */
    custom(scheme: string, resource: string): string;
}
//# sourceMappingURL=uri-builder.d.ts.map