/**
 * Logger Service
 * Structured logging for the AI Teammates Framework
 */
import { safeJsonStringify } from '../../shared/utils.js';
export class Logger {
    agentName;
    logLevel;
    enableConsole;
    enableFile;
    logEntries = [];
    maxEntries;
    constructor(options) {
        this.agentName = options.agentName;
        this.logLevel = options.logLevel || 'info';
        this.enableConsole = options.enableConsole !== false;
        this.enableFile = options.enableFile || false;
        this.maxEntries = options.maxEntries || 1000;
    }
    /**
     * Log info message
     */
    info(message, context) {
        this.log('info', message, context);
    }
    /**
     * Log warning message
     */
    warn(message, context) {
        this.log('warn', message, context);
    }
    /**
     * Log error message
     */
    error(message, error, context) {
        const errorInfo = error ? {
            name: error.name,
            message: error.message,
            stack: error.stack
        } : undefined;
        this.log('error', message, context, errorInfo);
    }
    /**
     * Log debug message
     */
    debug(message, context) {
        this.log('debug', message, context);
    }
    /**
     * Core logging method
     */
    log(level, message, context, error) {
        if (!this.shouldLog(level)) {
            return;
        }
        const entry = {
            timestamp: new Date().toISOString(),
            level,
            message: `[${this.agentName}] ${message}`,
            context,
            error
        };
        // Store in memory
        this.logEntries.push(entry);
        if (this.logEntries.length > this.maxEntries) {
            this.logEntries.shift();
        }
        // Console output
        if (this.enableConsole) {
            this.logToConsole(entry);
        }
        // File output (if enabled)
        if (this.enableFile) {
            this.logToFile(entry);
        }
    }
    /**
     * Check if should log at this level
     */
    shouldLog(level) {
        const levels = {
            debug: 0,
            info: 1,
            warn: 2,
            error: 3
        };
        return levels[level] >= levels[this.logLevel];
    }
    /**
     * Log to console
     */
    logToConsole(entry) {
        const timestamp = entry.timestamp.substring(11, 19); // HH:MM:SS
        const prefix = `${timestamp} [${entry.level.toUpperCase()}]`;
        let output = `${prefix} ${entry.message}`;
        if (entry.context) {
            output += ` ${safeJsonStringify(entry.context)}`;
        }
        if (entry.error) {
            output += `\nError: ${entry.error.name}: ${entry.error.message}`;
            if (entry.error.stack) {
                output += `\n${entry.error.stack}`;
            }
        }
        switch (entry.level) {
            case 'debug':
                console.debug(output);
                break;
            case 'info':
                console.info(output);
                break;
            case 'warn':
                console.warn(output);
                break;
            case 'error':
                console.error(output);
                break;
        }
    }
    /**
     * Log to file (placeholder - would need file system implementation)
     */
    logToFile(entry) {
        // TODO: Implement file logging if needed
        // This would require fs module and proper file handling
    }
    /**
     * Get recent log entries
     */
    getRecentLogs(count = 50) {
        return this.logEntries.slice(-count);
    }
    /**
     * Get logs by level
     */
    getLogsByLevel(level) {
        return this.logEntries.filter(entry => entry.level === level);
    }
    /**
     * Get logs in time range
     */
    getLogsByTimeRange(startTime, endTime) {
        return this.logEntries.filter(entry => {
            const entryTime = new Date(entry.timestamp);
            return entryTime >= startTime && entryTime <= endTime;
        });
    }
    /**
     * Search logs by message content
     */
    searchLogs(query) {
        const lowerQuery = query.toLowerCase();
        return this.logEntries.filter(entry => entry.message.toLowerCase().includes(lowerQuery));
    }
    /**
     * Clear all log entries
     */
    clearLogs() {
        this.logEntries.length = 0;
    }
    /**
     * Get logging statistics
     */
    getStats() {
        const entriesByLevel = {
            debug: 0,
            info: 0,
            warn: 0,
            error: 0
        };
        for (const entry of this.logEntries) {
            entriesByLevel[entry.level]++;
        }
        return {
            totalEntries: this.logEntries.length,
            entriesByLevel,
            oldestEntry: this.logEntries[0]?.timestamp,
            newestEntry: this.logEntries[this.logEntries.length - 1]?.timestamp
        };
    }
    /**
     * Create child logger with additional context
     */
    child(context) {
        const childLogger = new Logger({
            agentName: this.agentName,
            logLevel: this.logLevel,
            enableConsole: this.enableConsole,
            enableFile: this.enableFile,
            maxEntries: this.maxEntries
        });
        // Override log method to include parent context
        const originalLog = childLogger.log.bind(childLogger);
        childLogger.log = (level, message, childContext, error) => {
            const mergedContext = { ...context, ...childContext };
            originalLog(level, message, mergedContext, error);
        };
        return childLogger;
    }
}
//# sourceMappingURL=logger.js.map