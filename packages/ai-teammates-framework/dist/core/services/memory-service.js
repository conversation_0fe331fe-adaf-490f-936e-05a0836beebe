/**
 * Memory Service
 * In-memory storage for agent state and context
 */
import { safeJsonStringify, safeJsonParse } from '../../shared/utils.js';
export class MemoryService {
    memory = new Map();
    /**
     * Set a value in memory
     */
    set(key, value) {
        if (!key) {
            throw new Error('Memory key cannot be empty');
        }
        this.memory.set(key, value);
    }
    /**
     * Get a value from memory
     */
    get(key) {
        if (!key) {
            throw new Error('Memory key cannot be empty');
        }
        return this.memory.get(key);
    }
    /**
     * Check if key exists in memory
     */
    has(key) {
        if (!key) {
            return false;
        }
        return this.memory.has(key);
    }
    /**
     * Delete a key from memory
     */
    delete(key) {
        if (!key) {
            return false;
        }
        return this.memory.delete(key);
    }
    /**
     * Clear all memory
     */
    clear() {
        this.memory.clear();
    }
    /**
     * Get all memory as object
     */
    getAll() {
        const result = {};
        for (const [key, value] of this.memory.entries()) {
            result[key] = value;
        }
        return result;
    }
    /**
     * Get memory size
     */
    size() {
        return this.memory.size;
    }
    /**
     * Get all keys
     */
    keys() {
        return Array.from(this.memory.keys());
    }
    /**
     * Get all values
     */
    values() {
        return Array.from(this.memory.values());
    }
    /**
     * Set multiple values at once
     */
    setMultiple(entries) {
        for (const [key, value] of Object.entries(entries)) {
            this.set(key, value);
        }
    }
    /**
     * Get multiple values at once
     */
    getMultiple(keys) {
        const result = {};
        for (const key of keys) {
            if (this.has(key)) {
                result[key] = this.get(key);
            }
        }
        return result;
    }
    /**
     * Delete multiple keys at once
     */
    deleteMultiple(keys) {
        let deletedCount = 0;
        for (const key of keys) {
            if (this.delete(key)) {
                deletedCount++;
            }
        }
        return deletedCount;
    }
    /**
     * Serialize memory to JSON string
     */
    serialize() {
        return safeJsonStringify(this.getAll(), 2);
    }
    /**
     * Deserialize memory from JSON string
     */
    deserialize(json) {
        const data = safeJsonParse(json, {});
        this.clear();
        this.setMultiple(data);
    }
    /**
     * Create a snapshot of current memory
     */
    snapshot() {
        return { ...this.getAll() };
    }
    /**
     * Restore memory from snapshot
     */
    restore(snapshot) {
        this.clear();
        this.setMultiple(snapshot);
    }
    /**
     * Get memory usage statistics
     */
    getStats() {
        const serialized = this.serialize();
        return {
            size: this.size(),
            keys: this.keys().length,
            memoryUsage: `${Math.round(serialized.length / 1024)} KB`
        };
    }
    /**
     * Find keys matching pattern
     */
    findKeys(pattern) {
        const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
        return this.keys().filter(key => regex.test(key));
    }
    /**
     * Get values for keys matching pattern
     */
    findValues(pattern) {
        const matchingKeys = this.findKeys(pattern);
        return this.getMultiple(matchingKeys);
    }
    /**
     * Increment numeric value (or set to 1 if doesn't exist)
     */
    increment(key, amount = 1) {
        const current = this.get(key) || 0;
        const newValue = (typeof current === 'number' ? current : 0) + amount;
        this.set(key, newValue);
        return newValue;
    }
    /**
     * Decrement numeric value (or set to -amount if doesn't exist)
     */
    decrement(key, amount = 1) {
        return this.increment(key, -amount);
    }
    /**
     * Append to array value (or create array if doesn't exist)
     */
    append(key, value) {
        const current = this.get(key);
        const array = Array.isArray(current) ? current : [];
        array.push(value);
        this.set(key, array);
        return array;
    }
    /**
     * Prepend to array value (or create array if doesn't exist)
     */
    prepend(key, value) {
        const current = this.get(key);
        const array = Array.isArray(current) ? current : [];
        array.unshift(value);
        this.set(key, array);
        return array;
    }
}
//# sourceMappingURL=memory-service.js.map