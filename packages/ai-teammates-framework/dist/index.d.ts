/**
 * AI Teammates Framework
 * Generic, reusable framework for building AI teammates with plugin architecture
 */
export { ConfigSystem, TeammateEngine } from './core/index.js';
export { URIBuilder, MemoryService, Logger } from './core/services/index.js';
export * from './plugins/index.js';
export type * from './shared/types.js';
export { BaseFrameworkError, ConfigurationError, PluginError, ToolExecutionError, ResourceError, IntegrationError, ErrorFactory, ErrorHandler } from './shared/errors.js';
export * from './shared/utils.js';
export { TeammateEngine as Framework } from './core/teammate-engine.js';
export { ConfigSystem as Config } from './core/config-system.js';
//# sourceMappingURL=index.d.ts.map