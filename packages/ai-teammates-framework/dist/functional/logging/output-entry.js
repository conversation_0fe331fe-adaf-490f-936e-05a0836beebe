/**
 * Output log entry function
 */
import { formatLogEntry } from './format-entry.js';
export const outputLogEntry = (entry) => {
    const formatted = formatLogEntry(entry);
    switch (entry.level) {
        case 'debug':
        case 'info':
            console.info(formatted);
            break;
        case 'warn':
            console.warn(formatted);
            break;
        case 'error':
            console.error(formatted);
            break;
    }
};
//# sourceMappingURL=output-entry.js.map