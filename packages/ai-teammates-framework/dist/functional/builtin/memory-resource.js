/**
 * Built-in memory resource
 */
import { success } from '../utils/result.js';
import { createResource } from '../tools/create-resource.js';
const memoryResourceHandler = async (uri) => {
    const key = uri.split('/').pop() || '';
    return success(`Memory resource for key: ${decodeURIComponent(key)}`);
};
export const createMemoryResource = (uriPrefix) => createResource(`${uriPrefix}://memory/{key}`, 'Memory Storage', 'Access stored memory values', 'text/plain', memoryResourceHandler);
//# sourceMappingURL=memory-resource.js.map