/**
 * Built-in conversation prompt
 */
import { success } from '../utils/result.js';
import { createPrompt } from '../tools/create-prompt.js';
const conversationPromptArgs = [
    {
        name: 'context',
        description: 'Conversation context or topic',
        required: true
    },
    {
        name: 'style',
        description: 'Communication style preference',
        required: false
    }
];
const conversationPromptHandler = async (args) => {
    const context = args.context;
    const style = args.style || 'professional';
    const prompt = `You are an AI teammate engaging in conversation about: ${context}
Communication style: ${style}
Please provide helpful, accurate, and contextually appropriate responses.`;
    return success(prompt);
};
export const createConversationPrompt = () => createPrompt('conversation', 'Generate conversation prompts for AI teammate interactions', conversationPromptArgs, conversationPromptHandler);
//# sourceMappingURL=conversation-prompt.js.map