/**
 * Functional URI builder service composition
 */
import { buildMemoryUri } from '../uri/build-memory.js';
import { buildConversationUri } from '../uri/build-conversation.js';
import { buildTemplateUri } from '../uri/build-template.js';
import { buildCurrentProjectUri } from '../uri/build-project.js';
export const createUriBuilderService = (prefix) => ({
    memory: (key) => buildMemoryUri(prefix, key),
    conversations: (id) => buildConversationUri(prefix, id),
    templates: (name) => buildTemplateUri(prefix, name),
    currentProject: () => buildCurrentProjectUri(prefix)
});
//# sourceMappingURL=uri-builder.js.map