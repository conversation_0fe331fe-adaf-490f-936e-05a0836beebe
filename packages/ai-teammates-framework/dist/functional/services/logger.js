/**
 * Functional logger service composition
 */
import { createLogEntry } from '../logging/create-entry.js';
import { outputLogEntry } from '../logging/output-entry.js';
import { pipe } from '../utils/compose.js';
export const createLoggerService = (agentName, enableConsole = true) => {
    const logWithOutput = (entry) => {
        if (enableConsole) {
            outputLogEntry(entry);
        }
        return entry;
    };
    const createAndOutput = (level) => (message, context = {}) => pipe(createLogEntry(level, message, agentName, context))
        .to(logWithOutput)
        .value();
    return {
        info: createAndOutput('info'),
        warn: createAndOutput('warn'),
        error: createAndOutput('error'),
        debug: createAndOutput('debug')
    };
};
//# sourceMappingURL=logger.js.map