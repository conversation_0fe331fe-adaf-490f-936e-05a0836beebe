/**
 * Functional memory service composition
 */
import type { ReadonlyMemoryEntry } from '../types/features.js';
export interface MemoryService {
    readonly set: <T>(key: string, value: T, ttl?: number) => ReadonlyMemoryEntry<T>;
    readonly get: <T>(key: string, entries: readonly ReadonlyMemoryEntry<T>[]) => T | undefined;
    readonly has: (key: string, entries: readonly ReadonlyMemoryEntry[]) => boolean;
    readonly delete: (key: string, entries: readonly ReadonlyMemoryEntry[]) => readonly ReadonlyMemoryEntry[];
    readonly clear: () => readonly ReadonlyMemoryEntry[];
    readonly getAll: (entries: readonly ReadonlyMemoryEntry[]) => readonly ReadonlyMemoryEntry[];
    readonly size: (entries: readonly ReadonlyMemoryEntry[]) => number;
}
export declare const createMemoryService: () => MemoryService;
//# sourceMappingURL=memory.d.ts.map