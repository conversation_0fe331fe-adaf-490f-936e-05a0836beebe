/**
 * Functional memory service composition
 */
import { createMemoryEntry } from '../memory/create-entry.js';
import { filterValidMemoryEntries } from '../memory/filter-valid.js';
export const createMemoryService = () => ({
    set: (key, value, ttl) => createMemoryEntry(key, value, ttl),
    get: (key, entries) => {
        const validEntries = filterValidMemoryEntries(entries);
        const entry = validEntries.find(e => e.key === key);
        return entry?.value;
    },
    has: (key, entries) => {
        const validEntries = filterValidMemoryEntries(entries);
        return validEntries.some(e => e.key === key);
    },
    delete: (key, entries) => filterValidMemoryEntries(entries).filter(e => e.key !== key),
    clear: () => [],
    getAll: (entries) => filterValidMemoryEntries(entries),
    size: (entries) => filterValidMemoryEntries(entries).length
});
//# sourceMappingURL=memory.js.map