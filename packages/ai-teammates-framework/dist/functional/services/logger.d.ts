/**
 * Functional logger service composition
 */
import type { ReadonlyLogEntry, ReadonlyRecord } from '../types/features.js';
export interface LoggerService {
    readonly info: (message: string, context?: ReadonlyRecord<string, unknown>) => ReadonlyLogEntry;
    readonly warn: (message: string, context?: ReadonlyRecord<string, unknown>) => ReadonlyLogEntry;
    readonly error: (message: string, context?: ReadonlyRecord<string, unknown>) => ReadonlyLogEntry;
    readonly debug: (message: string, context?: ReadonlyRecord<string, unknown>) => ReadonlyLogEntry;
}
export declare const createLoggerService: (agentName: string, enableConsole?: boolean) => LoggerService;
//# sourceMappingURL=logger.d.ts.map