/**
 * Functional URI builder service composition
 */
export interface UriBuilderService {
    readonly memory: (key: string) => string;
    readonly conversations: (id: string) => string;
    readonly templates: (name: string) => string;
    readonly currentProject: () => string;
}
export declare const createUriBuilderService: (prefix: string) => UriBuilderService;
//# sourceMappingURL=uri-builder.d.ts.map