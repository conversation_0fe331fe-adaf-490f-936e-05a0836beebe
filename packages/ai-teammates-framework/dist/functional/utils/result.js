/**
 * Result utility functions for functional error handling
 */
export const success = (data) => ({
    success: true,
    data
});
export const failure = (error) => ({
    success: false,
    error
});
export const isSuccess = (result) => result.success === true;
export const isFailure = (result) => result.success === false;
export const map = (result, fn) => isSuccess(result) ? success(fn(result.data)) : result;
export const flatMap = (result, fn) => isSuccess(result) ? fn(result.data) : result;
export const mapError = (result, fn) => isFailure(result) ? failure(fn(result.error)) : result;
//# sourceMappingURL=result.js.map