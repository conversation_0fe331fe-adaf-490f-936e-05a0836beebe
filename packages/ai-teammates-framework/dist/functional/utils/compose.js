/**
 * Function composition utilities
 */
export const pipe = (value) => ({
    to: (fn) => pipe(fn(value)),
    value: () => value
});
export const compose = (f, g) => (a) => f(g(a));
export const curry = (fn) => (a) => (b) => fn(a, b);
export const partial = (fn, a) => (b) => fn(a, b);
export const identity = (value) => value;
export const constant = (value) => () => value;
export const tap = (fn) => (value) => {
    fn(value);
    return value;
};
export const when = (predicate, fn) => (value) => predicate(value) ? fn(value) : value;
//# sourceMappingURL=compose.js.map