/**
 * Function composition utilities
 */
export declare const pipe: <T>(value: T) => {
    to: <U>(fn: (input: T) => U) => {
        to: <U_1>(fn: (input: U) => U_1) => {
            to: <U_2>(fn: (input: U_1) => U_2) => {
                to: <U_3>(fn: (input: U_2) => U_3) => {
                    to: <U_4>(fn: (input: U_3) => U_4) => {
                        to: <U_5>(fn: (input: U_4) => U_5) => {
                            to: <U_6>(fn: (input: U_5) => U_6) => {
                                to: <U_7>(fn: (input: U_6) => U_7) => {
                                    to: <U_8>(fn: (input: U_7) => U_8) => {
                                        to: <U_9>(fn: (input: U_8) => U_9) => {
                                            to: <U_10>(fn: (input: U_9) => U_10) => /*elided*/ any;
                                            value: () => U_9;
                                        };
                                        value: () => U_8;
                                    };
                                    value: () => U_7;
                                };
                                value: () => U_6;
                            };
                            value: () => U_5;
                        };
                        value: () => U_4;
                    };
                    value: () => U_3;
                };
                value: () => U_2;
            };
            value: () => U_1;
        };
        value: () => U;
    };
    value: () => T;
};
export declare const compose: <A, B, C>(f: (b: B) => C, g: (a: A) => B) => (a: A) => C;
export declare const curry: <A, B, C>(fn: (a: A, b: B) => C) => (a: A) => (b: B) => C;
export declare const partial: <A, B, C>(fn: (a: A, b: B) => C, a: A) => (b: B) => C;
export declare const identity: <T>(value: T) => T;
export declare const constant: <T>(value: T) => () => T;
export declare const tap: <T>(fn: (value: T) => void) => (value: T) => T;
export declare const when: <T>(predicate: (value: T) => boolean, fn: (value: T) => T) => (value: T) => T;
//# sourceMappingURL=compose.d.ts.map