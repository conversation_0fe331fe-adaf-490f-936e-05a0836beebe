/**
 * Result utility functions for functional error handling
 */
import type { Result, Success, Failure } from '../types/result.js';
export declare const success: <T>(data: T) => Success<T>;
export declare const failure: <E = string>(error: E) => Failure<E>;
export declare const isSuccess: <T, E>(result: Result<T, E>) => result is Success<T>;
export declare const isFailure: <T, E>(result: Result<T, E>) => result is Failure<E>;
export declare const map: <T, U, E>(result: Result<T, E>, fn: (data: T) => U) => Result<U, E>;
export declare const flatMap: <T, U, E>(result: Result<T, E>, fn: (data: T) => Result<U, E>) => Result<U, E>;
export declare const mapError: <T, E, F>(result: Result<T, E>, fn: (error: E) => F) => Result<T, F>;
//# sourceMappingURL=result.d.ts.map