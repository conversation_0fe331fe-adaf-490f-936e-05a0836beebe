/**
 * Handle MCP resource read function
 */
import { createMCPResponse } from './create-response.js';
import { isSuccess } from '../utils/result.js';
export const handleResourceRead = async (request, resources) => {
    const uri = request.params?.uri;
    if (!uri) {
        return createMCPResponse('Resource URI is required', true);
    }
    const resource = resources.find(r => r.uri === uri);
    if (!resource) {
        return createMCPResponse(`Resource '${uri}' not found`, true);
    }
    try {
        const result = await resource.handler(uri);
        if (isSuccess(result)) {
            return createMCPResponse(result.data);
        }
        else {
            return createMCPResponse(result.error, true);
        }
    }
    catch (error) {
        return createMCPResponse(`Resource read failed: ${String(error)}`, true);
    }
};
//# sourceMappingURL=handle-resource-read.js.map