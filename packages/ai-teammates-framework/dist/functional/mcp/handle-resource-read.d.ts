/**
 * Handle MCP resource read function
 */
import type { ReadonlyMCPResourceRequest, ReadonlyMCPResponse } from '../types/mcp.js';
import type { ReadonlyResourceDefinition } from '../types/plugin.js';
export declare const handleResourceRead: (request: ReadonlyMCPResourceRequest, resources: readonly ReadonlyResourceDefinition[]) => Promise<ReadonlyMCPResponse>;
//# sourceMappingURL=handle-resource-read.d.ts.map