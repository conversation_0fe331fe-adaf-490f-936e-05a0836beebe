/**
 * Create functional framework composition
 */
import { createLoggerService } from '../services/logger.js';
import { createMemoryService } from '../services/memory.js';
import { createUriBuilderService } from '../services/uri-builder.js';
export const createFramework = (config, tools = [], resources = [], prompts = []) => ({
    config,
    logger: createLoggerService(config.agent.name),
    memory: createMemoryService(),
    uriBuilder: createUriBuilderService(config.features.resources.uriPrefix),
    tools,
    resources,
    prompts
});
//# sourceMappingURL=create-framework.js.map