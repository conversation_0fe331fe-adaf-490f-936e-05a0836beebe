/**
 * Base Plugin
 * Abstract base class for teammate plugins
 */
import type { TeammatePlugin, PersonalityConfig, ToolDefinition, ResourceDefinition, PromptDefinition, PluginContext } from '../shared/types.js';
export declare abstract class BasePlugin implements TeammatePlugin {
    protected context?: PluginContext;
    /**
     * Get plugin name
     */
    abstract getName(): string;
    /**
     * Get plugin role
     */
    abstract getRole(): string;
    /**
     * Get personality configuration
     */
    abstract getPersonality(): PersonalityConfig;
    /**
     * Get tool definitions
     */
    abstract getTools(): ToolDefinition[];
    /**
     * Get resource definitions
     */
    abstract getResources(): ResourceDefinition[];
    /**
     * Get prompt definitions
     */
    abstract getPrompts(): PromptDefinition[];
    /**
     * Initialize plugin with context
     */
    initialize(context: PluginContext): Promise<void>;
    /**
     * Override in subclasses for custom initialization logic
     */
    protected onInitialize(context: PluginContext): Promise<void>;
    /**
     * Get plugin context (available after initialization)
     */
    protected getContext(): PluginContext;
    /**
     * Helper method to create tool definition
     */
    protected createTool(name: string, description: string, schema: any, handler: (args: any, context: any) => Promise<any>): ToolDefinition;
    /**
     * Helper method to create resource definition
     */
    protected createResource(uri: string, name: string, description: string, mimeType: string, handler: () => Promise<any>): ResourceDefinition;
    /**
     * Helper method to create prompt definition
     */
    protected createPrompt(name: string, description: string, template: string, args?: Array<{
        name: string;
        description: string;
        required: boolean;
        type: 'string' | 'number' | 'boolean' | 'object';
    }>): PromptDefinition;
    /**
     * Helper method to get agent-specific tool name
     */
    protected getToolName(baseName: string): string;
    /**
     * Helper method to get agent-specific URI
     */
    protected getURI(type: 'memory' | 'template' | 'document', resource: string): string;
    /**
     * Helper method to log from plugin
     */
    protected log(level: 'debug' | 'info' | 'warn' | 'error', message: string, context?: Record<string, any>): void;
}
//# sourceMappingURL=base-plugin.d.ts.map