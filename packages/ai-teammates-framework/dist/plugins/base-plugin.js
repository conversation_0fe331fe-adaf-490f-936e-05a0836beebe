/**
 * Base Plugin
 * Abstract base class for teammate plugins
 */
export class BasePlugin {
    context;
    /**
     * Initialize plugin with context
     */
    async initialize(context) {
        this.context = context;
        context.logger.info(`Initializing plugin: ${this.getName()}`);
        // Override in subclasses for custom initialization
        await this.onInitialize(context);
        context.logger.info(`Plugin initialized: ${this.getName()}`);
    }
    /**
     * Override in subclasses for custom initialization logic
     */
    async onInitialize(_context) {
        // Default implementation does nothing
    }
    /**
     * Get plugin context (available after initialization)
     */
    getContext() {
        if (!this.context) {
            throw new Error('Plugin not initialized - context not available');
        }
        return this.context;
    }
    /**
     * Helper method to create tool definition
     */
    createTool(name, description, schema, handler) {
        return {
            name,
            description,
            schema,
            handler
        };
    }
    /**
     * Helper method to create resource definition
     */
    createResource(uri, name, description, mimeType, handler) {
        return {
            uri,
            name,
            description,
            mimeType,
            handler
        };
    }
    /**
     * Helper method to create prompt definition
     */
    createPrompt(name, description, template, args = []) {
        return {
            name,
            description,
            template,
            arguments: args
        };
    }
    /**
     * Helper method to get agent-specific tool name
     */
    getToolName(baseName) {
        const context = this.getContext();
        const prefix = context.config.features.tools.namePrefix;
        return `${prefix}-${baseName}`;
    }
    /**
     * Helper method to get agent-specific URI
     */
    getURI(type, resource) {
        const context = this.getContext();
        const uriBuilder = context.uriBuilder;
        switch (type) {
            case 'memory':
                return uriBuilder.memory(resource);
            case 'template':
                return uriBuilder.template(resource);
            case 'document':
                return uriBuilder.document(resource);
            default:
                throw new Error(`Unknown URI type: ${type}`);
        }
    }
    /**
     * Helper method to log from plugin
     */
    log(level, message, context) {
        const pluginContext = this.getContext();
        const logger = pluginContext.logger;
        switch (level) {
            case 'debug':
                logger.debug(message, context);
                break;
            case 'info':
                logger.info(message, context);
                break;
            case 'warn':
                logger.warn(message, context);
                break;
            case 'error':
                logger.error(message, undefined, context);
                break;
        }
    }
}
//# sourceMappingURL=base-plugin.js.map