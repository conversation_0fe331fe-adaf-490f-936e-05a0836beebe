---
name: Epic Issue
about: Create an Epic for major feature areas
title: 'EPIC: [Epic Name]'
labels: epic, phase-2
assignees: ''
---

## Epic Overview

**Epic ID:** EPIC-XXX  
**Domain Reference:** [SRS Domain X - Domain Name]  
**Priority:** High/Medium/Low  
**Estimated Effort:** X% of total project  

## Epic Description
[Detailed description of what this epic encompasses]

## Key Capabilities
- [ ] Capability 1
- [ ] Capability 2
- [ ] Capability 3

## Functional Requirements Included
- **FR-DX-001:** [Requirement Name]
- **FR-DX-002:** [Requirement Name]
- **FR-DX-003:** [Requirement Name]

## Success Criteria
- [ ] Success criterion 1
- [ ] Success criterion 2
- [ ] Success criterion 3

## Dependencies
- **Depends on:** [Other EPICs or requirements]
- **Blocks:** [What this EPIC blocks]

## Implementation Phases
- [ ] **Phase 2.1:** FRS Creation
- [ ] **Phase 2.2:** Implementation Plan
- [ ] **Phase 2.3:** Work Breakdown Structure
- [ ] **Phase 2.4:** Code Development

## Related Issues
[This section will be populated with child issues during WBS phase]

## Definition of Done
- [ ] All functional requirements implemented
- [ ] All success criteria met
- [ ] Code reviewed and merged
- [ ] Documentation updated
- [ ] Tests passing

---

**Epic Status:** Planning/In Progress/Complete  
**Last Updated:** [Date]
