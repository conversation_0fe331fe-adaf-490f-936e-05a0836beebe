{"name": "mcp-realtime-experiment", "version": "0.1.0", "private": true, "description": "MCP Server for real-time data injection experiment", "main": "src/index.ts", "exports": {".": "./src/index.ts"}, "bin": {"mcp-realtime": "dist/index.js"}, "keywords": ["mcp", "realtime", "experiment", "supabase"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "@supabase/supabase-js": "^2.39.0", "dotenv": "^16.3.1", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}