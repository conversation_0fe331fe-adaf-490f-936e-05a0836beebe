🚀 Starting Single Scenario Test
============================================================

🧪 Starting: Idle State Injection
📝 Description: Send messages when agent is not actively responding
⏱️  Delay: 1000ms, Messages: 3
🆔 Conversation ID: 454f5da0-09b6-4598-b13c-627ab0ff846b
────────────────────────────────────────────────────────────
✅ Inserted user message: Test message 1 - 2025-06-06T15:45:46.739Z...
✅ Inserted user message: Test message 2 - 2025-06-06T15:45:48.117Z...
✅ Inserted user message: Test message 3 - 2025-06-06T15:45:49.305Z...
✅ Completed: Idle State Injection
📊 Messages inserted: 3
⏱️  Duration: 2755ms

📊 TEST SUMMARY
============================================================
✅ Successful scenarios: 1
❌ Failed scenarios: 0
📈 Total scenarios: 1

📋 Detailed Results:
✅ Idle State Injection (IDLE_STATE)
   📊 3 messages in 2755ms
   🆔 Conversation: 454f5da0-09b6-4598-b13c-627ab0ff846b

🔬 Experiment completed. Check Augment Code behavior for analysis.
agent2@WINDOWS:~/AISDLC/experiments/mcp-realtime-injection/test-scripts$ 