{"compilerOptions": {"target": "ES2021", "lib": ["ES2021", "DOM"], "module": "CommonJS", "moduleResolution": "Node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "dist", "composite": true, "baseUrl": "."}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}