{"name": "experimental-teammate", "version": "1.0.0", "description": "Experimental AI teammate for testing new MCP capabilities", "type": "module", "main": "dist/index.js", "bin": {"experimental-teammate": "dist/index.js"}, "keywords": ["mcp", "model-context-protocol", "experimental", "ai-teammate", "testing"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "cors": "^2.8.5", "eventsource": "^4.0.0", "express": "^4.18.2", "zod": "^3.25.53"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}